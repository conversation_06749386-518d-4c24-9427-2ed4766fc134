# Mario DS Multiplayer Implementation

This document describes the comprehensive multiplayer support added to the Mario DS game.

## Features

### 1. Local Multiplayer
- **Two players on the same machine**
- **Player 1 Controls**: WASD + Space (fire)
- **Player 2 Controls**: Arrow Keys + P (fire)
- **Visual Differentiation**: Different sprites and colors for each player
- **Separate Starting Positions**: Players start at different X coordinates
- **Shared Camera**: Camera follows both players intelligently

### 2. Network Multiplayer
- **Client-Server Architecture**: One player hosts, another connects
- **Real-time Synchronization**: Player positions and actions synced over network
- **Automatic Reconnection**: Client attempts to reconnect if connection is lost
- **Server Authority**: Host has authority over game state and timing

## Architecture

### Core Components

1. **MultiplayerManager** (`src/manager/MultiplayerManager.java`)
   - Central coordinator for all multiplayer functionality
   - Handles mode switching and input routing
   - Manages network connections and player state

2. **GameStateManager** (`src/manager/GameStateManager.java`)
   - Handles game state synchronization
   - Implements client prediction and server reconciliation
   - Maintains player state history for lag compensation

3. **Network Layer** (`src/manager/network/`)
   - **NetworkServer**: Handles hosting and client connections
   - **NetworkClient**: Manages connection to host
   - **ConnectionManager**: Automatic connection management and reconnection
   - **NetworkMessage**: Serializable message types for communication
   - **NetworkUtils**: Utility functions for network operations

4. **Input System**
   - **InputBuffer**: Buffers and manages input events for network sync
   - **KeyBindingManager**: Routes input to appropriate multiplayer handlers

### Game Modes

```java
public enum GameMode {
    LOCAL_SINGLE_PLAYER,    // Traditional single player
    LOCAL_MULTIPLAYER,      // Two players, same machine
    NETWORK_HOST,           // Host a network game
    NETWORK_CLIENT          // Connect to a host
}
```

## Usage

### Starting Local Multiplayer
1. From main menu, select "Multiplayer"
2. Choose "Local Multiplayer"
3. Select a map and start playing
4. Player 1: WASD + Space, Player 2: Arrow Keys + P

### Starting Network Multiplayer

#### As Host:
1. From main menu, select "Multiplayer"
2. Choose "Network Host"
3. Game will display your IP address (e.g., "*************:12345")
4. Share this IP with other players
5. Wait for client to connect, then select map

#### As Client:
1. From main menu, select "Multiplayer"
2. Choose "Network Client"
3. Game will attempt to connect to localhost (for testing)
4. In full implementation, you would enter the host's IP address

## Network Protocol

### Message Types
- **PLAYER_INPUT**: Transmits button presses and releases
- **GAME_STATE**: Synchronizes player positions and game state
- **CONNECTION_REQUEST/RESPONSE**: Handles initial connection
- **PING/PONG**: Connection health monitoring
- **DISCONNECT**: Clean disconnection

### Synchronization Strategy
- **Server Authority**: Host controls game logic and timing
- **Client Prediction**: Clients predict movement for responsiveness
- **State Reconciliation**: Periodic sync to prevent drift
- **Input Buffering**: Handles network delays and packet loss

## Testing

### Automated Tests
Run the test suite to validate multiplayer functionality:

```bash
# Compile and run tests
javac -cp "lib/*" -d bin src/manager/TestRunner.java
java -cp "bin:lib/*" manager.TestRunner
```

### Manual Testing

#### Local Multiplayer:
1. Start local multiplayer mode
2. Verify both players can move independently
3. Check that camera follows both players
4. Confirm different sprites are used
5. Test collision detection works for both players

#### Network Multiplayer:
1. Start host on one machine/instance
2. Note the displayed IP address
3. Start client on another machine/instance
4. Verify connection is established
5. Test that input from each player affects their character
6. Verify game state stays synchronized

## Configuration

### Network Settings
- **Default Port**: 12345
- **Connection Timeout**: 10 seconds
- **Reconnection Attempts**: 5 maximum
- **Sync Rate**: ~60 FPS (16ms intervals)

### Customization
To change network settings, modify constants in:
- `NetworkServer.java`: Server configuration
- `NetworkClient.java`: Client configuration
- `ConnectionManager.java`: Connection management

## Troubleshooting

### Common Issues

1. **"Connection Failed"**
   - Check firewall settings
   - Verify IP address is correct
   - Ensure host is running and waiting

2. **"Players Overlapping in Local Mode"**
   - Check Mario.resetLocation() method
   - Verify different starting positions are set

3. **"Input Lag in Network Mode"**
   - Check network latency
   - Verify sync rate settings
   - Consider adjusting prediction parameters

4. **"Game Desynchronized"**
   - Check GameStateManager configuration
   - Verify server authority is working
   - Review reconciliation logic

### Debug Information
Enable debug output by checking console messages. The game prints:
- Connection status changes
- Network errors and reconnection attempts
- Game state synchronization events
- Input routing information

## Future Enhancements

### Planned Features
1. **IP Entry Dialog**: GUI for entering host IP address
2. **Lobby System**: Pre-game lobby with chat
3. **Spectator Mode**: Allow observers to watch games
4. **Tournament Mode**: Multiple players, elimination rounds
5. **Replay System**: Record and playback multiplayer games

### Performance Optimizations
1. **Delta Compression**: Only send changed state
2. **Interpolation**: Smooth movement between updates
3. **Lag Compensation**: Better handling of network delays
4. **Bandwidth Optimization**: Reduce network traffic

## Technical Notes

### Thread Safety
- All network operations use thread-safe collections
- Input buffering is designed for concurrent access
- Connection management uses atomic operations

### Memory Management
- Automatic cleanup of old state history
- Resource cleanup on disconnection
- Bounded buffer sizes to prevent memory leaks

### Error Handling
- Graceful degradation on network failures
- Automatic fallback to single-player mode
- Comprehensive error logging and reporting

## Dependencies

The multiplayer implementation uses only standard Java libraries:
- `java.net.*` for networking
- `java.util.concurrent.*` for threading
- `java.io.*` for serialization
- No external dependencies required

## Compatibility

- **Java Version**: Requires Java 8 or higher
- **Network**: Works on LAN and localhost
- **Platform**: Cross-platform (Windows, macOS, Linux)
- **Backward Compatibility**: Single-player mode unchanged
