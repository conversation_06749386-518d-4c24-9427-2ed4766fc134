package manager;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.Iterator;

import manager.network.NetworkMessage.PlayerState;

/**
 * Maintains a history of player states for reconciliation and prediction.
 * Stores timestamped player states and provides methods to query historical data.
 */
public class PlayerStateHistory {
    
    private static final int MAX_HISTORY_SIZE = 120; // 2 seconds at 60 FPS
    private static final long STATE_INTERPOLATION_THRESHOLD_MS = 50; // 50ms
    
    private final ConcurrentLinkedQueue<TimestampedPlayerState> stateHistory;
    
    public PlayerStateHistory() {
        this.stateHistory = new ConcurrentLinkedQueue<>();
    }
    
    /**
     * Add a new player state to the history
     */
    public void addState(PlayerState state, long timestamp) {
        if (state == null) {
            return;
        }
        
        TimestampedPlayerState timestampedState = new TimestampedPlayerState(state, timestamp);
        stateHistory.offer(timestampedState);
        
        // Limit history size
        while (stateHistory.size() > MAX_HISTORY_SIZE) {
            stateHistory.poll();
        }
    }
    
    /**
     * Get the most recent player state
     */
    public PlayerState getLatestState() {
        TimestampedPlayerState latest = null;
        
        for (TimestampedPlayerState state : stateHistory) {
            if (latest == null || state.timestamp > latest.timestamp) {
                latest = state;
            }
        }
        
        return latest != null ? latest.state : null;
    }
    
    /**
     * Get player state at a specific timestamp (with interpolation)
     */
    public PlayerState getStateAtTime(long timestamp) {
        TimestampedPlayerState before = null;
        TimestampedPlayerState after = null;
        
        // Find the states before and after the target timestamp
        for (TimestampedPlayerState state : stateHistory) {
            if (state.timestamp <= timestamp) {
                if (before == null || state.timestamp > before.timestamp) {
                    before = state;
                }
            } else {
                if (after == null || state.timestamp < after.timestamp) {
                    after = state;
                }
            }
        }
        
        // If we have an exact match or very close match, return it
        if (before != null && Math.abs(before.timestamp - timestamp) < STATE_INTERPOLATION_THRESHOLD_MS) {
            return before.state;
        }
        
        if (after != null && Math.abs(after.timestamp - timestamp) < STATE_INTERPOLATION_THRESHOLD_MS) {
            return after.state;
        }
        
        // Interpolate between before and after states
        if (before != null && after != null) {
            return interpolateStates(before, after, timestamp);
        }
        
        // Return the closest available state
        if (before != null) {
            return before.state;
        }
        
        if (after != null) {
            return after.state;
        }
        
        return null;
    }
    
    /**
     * Interpolate between two player states
     */
    private PlayerState interpolateStates(TimestampedPlayerState before, TimestampedPlayerState after, long targetTime) {
        if (before.timestamp == after.timestamp) {
            return before.state;
        }
        
        // Calculate interpolation factor (0.0 to 1.0)
        double factor = (double)(targetTime - before.timestamp) / (after.timestamp - before.timestamp);
        factor = Math.max(0.0, Math.min(1.0, factor)); // Clamp to [0, 1]
        
        PlayerState beforeState = before.state;
        PlayerState afterState = after.state;
        
        // Interpolate position and velocity
        double x = lerp(beforeState.getX(), afterState.getX(), factor);
        double y = lerp(beforeState.getY(), afterState.getY(), factor);
        double velX = lerp(beforeState.getVelX(), afterState.getVelX(), factor);
        double velY = lerp(beforeState.getVelY(), afterState.getVelY(), factor);
        
        // For discrete values, use the "before" state (no interpolation)
        boolean jumping = beforeState.isJumping();
        boolean falling = beforeState.isFalling();
        boolean toRight = beforeState.isToRight();
        int lives = beforeState.getLives();
        int points = beforeState.getPoints();
        int coins = beforeState.getCoins();
        
        return new PlayerState(x, y, velX, velY, jumping, falling, toRight, lives, points, coins);
    }
    
    /**
     * Linear interpolation helper
     */
    private double lerp(double a, double b, double factor) {
        return a + (b - a) * factor;
    }
    
    /**
     * Get the number of states in history
     */
    public int getHistorySize() {
        return stateHistory.size();
    }
    
    /**
     * Get the oldest timestamp in history
     */
    public long getOldestTimestamp() {
        TimestampedPlayerState oldest = null;
        
        for (TimestampedPlayerState state : stateHistory) {
            if (oldest == null || state.timestamp < oldest.timestamp) {
                oldest = state;
            }
        }
        
        return oldest != null ? oldest.timestamp : 0;
    }
    
    /**
     * Get the newest timestamp in history
     */
    public long getNewestTimestamp() {
        TimestampedPlayerState newest = null;
        
        for (TimestampedPlayerState state : stateHistory) {
            if (newest == null || state.timestamp > newest.timestamp) {
                newest = state;
            }
        }
        
        return newest != null ? newest.timestamp : 0;
    }
    
    /**
     * Remove states older than the specified timestamp
     */
    public void cleanupOldStates(long cutoffTime) {
        Iterator<TimestampedPlayerState> iterator = stateHistory.iterator();
        
        while (iterator.hasNext()) {
            TimestampedPlayerState state = iterator.next();
            if (state.timestamp < cutoffTime) {
                iterator.remove();
            }
        }
    }
    
    /**
     * Clear all state history
     */
    public void clear() {
        stateHistory.clear();
    }
    
    /**
     * Check if history is empty
     */
    public boolean isEmpty() {
        return stateHistory.isEmpty();
    }
    
    /**
     * Get average time between states (for debugging)
     */
    public double getAverageTimeDelta() {
        if (stateHistory.size() < 2) {
            return 0.0;
        }
        
        long totalDelta = 0;
        int deltaCount = 0;
        TimestampedPlayerState previous = null;
        
        for (TimestampedPlayerState state : stateHistory) {
            if (previous != null) {
                totalDelta += Math.abs(state.timestamp - previous.timestamp);
                deltaCount++;
            }
            previous = state;
        }
        
        return deltaCount > 0 ? (double)totalDelta / deltaCount : 0.0;
    }
    
    /**
     * Get debug information about the state history
     */
    public String getDebugInfo() {
        if (isEmpty()) {
            return "Empty history";
        }
        
        return String.format("History: %d states, oldest: %d, newest: %d, avg delta: %.1fms",
                           getHistorySize(),
                           getOldestTimestamp(),
                           getNewestTimestamp(),
                           getAverageTimeDelta());
    }
    
    /**
     * Internal class to store timestamped player states
     */
    private static class TimestampedPlayerState {
        final PlayerState state;
        final long timestamp;
        
        TimestampedPlayerState(PlayerState state, long timestamp) {
            this.state = state;
            this.timestamp = timestamp;
        }
        
        @Override
        public String toString() {
            return String.format("State@%d: (%.1f, %.1f)", timestamp, state.getX(), state.getY());
        }
    }
}
