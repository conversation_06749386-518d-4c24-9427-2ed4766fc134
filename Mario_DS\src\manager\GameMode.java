package manager;

/**
 * Enumeration representing different game modes available in the Mario game.
 */
public enum GameMode {
	/**
	 * Single player mode - only one Mario character is active
	 */
	LOCAL_SINGLE_PLAYER,

	/**
	 * Local multiplayer mode - both Mario characters are controlled
	 * by players on the same PC using different input controls
	 */
	LOCAL_MULTIPLAYER,

	/**
	 * Network host mode - this instance acts as the server/host,
	 * running the authoritative game simulation and accepting client connections
	 */
	NETWORK_HOST,

	/**
	 * Network client mode - this instance connects to a host/server
	 * and sends input while receiving game state updates
	 */
	NETWORK_CLIENT;

	/**
	 * Check if the current game mode involves network communication
	 * 
	 * @return true if this is a network-based mode
	 */
	public boolean isNetworkMode() {
		return this == NETWORK_HOST || this == NETWORK_CLIENT;
	}

	/**
	 * Check if the current game mode supports multiple players
	 * 
	 * @return true if this mode supports multiple players
	 */
	public boolean isMultiplayer() {
		return this != LOCAL_SINGLE_PLAYER;
	}
}
