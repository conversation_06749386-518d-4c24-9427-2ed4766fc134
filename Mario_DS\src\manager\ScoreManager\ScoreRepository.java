package manager.ScoreManager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class ScoreRepository {

	public List<MarioScore> getAllScores() {
		List<MarioScore> scores = new ArrayList<>();
		String selectSQL = """
				SELECT mario_score, mario2_score, game_date
				FROM mario_scores
				ORDER BY game_date DESC
				""";

		try (Connection conn = DatabaseConfig.getConnection();
				Statement stmt = conn.createStatement();
				ResultSet rs = stmt.executeQuery(selectSQL)) {

			while (rs.next()) {
				int marioScore = rs.getInt("mario_score");
				int mario2Score = rs.getInt("mario2_score");
				LocalDateTime gameDate = rs.getTimestamp("game_date").toLocalDateTime();

				String dateString = gameDate.toString();
				MarioScore score = new MarioScore(marioScore, mario2Score, dateString);
				scores.add(score);
			}

		} catch (SQLException e) {
			System.err.println("Error retrieving scores from database: " + e.getMessage());
			e.printStackTrace();
		}

		return scores;
	}

	public List<MarioScore> getTopScores(int limit) {
		List<MarioScore> scores = new ArrayList<>();
		String selectSQL = """
				SELECT mario_score, mario2_score, game_date
				FROM mario_scores
				ORDER BY total_score DESC
				LIMIT ?
				""";

		try (Connection conn = DatabaseConfig.getConnection();
				PreparedStatement pstmt = conn.prepareStatement(selectSQL)) {

			pstmt.setInt(1, limit);
			ResultSet rs = pstmt.executeQuery();

			while (rs.next()) {
				int marioScore = rs.getInt("mario_score");
				int mario2Score = rs.getInt("mario2_score");
				LocalDateTime gameDate = rs.getTimestamp("game_date").toLocalDateTime();

				String dateString = gameDate.toString();
				MarioScore score = new MarioScore(marioScore, mario2Score, dateString);
				scores.add(score);
			}

		} catch (SQLException e) {
			System.err.println("Error retrieving top scores from database: " + e.getMessage());
			e.printStackTrace();
		}

		return scores;
	}

	public void deleteAllScores() {
		String deleteSQL = "DELETE FROM mario_scores";

		try (Connection conn = DatabaseConfig.getConnection();
				Statement stmt = conn.createStatement()) {

			int rowsDeleted = stmt.executeUpdate(deleteSQL);
			System.out.println("Deleted " + rowsDeleted + " scores from database");

		} catch (SQLException e) {
			System.err.println("Error deleting scores from database: " + e.getMessage());
			e.printStackTrace();
		}
	}

	public int getHighestScore() {
		String selectSQL = """
				SELECT MAX(total_score) as highest_score
				FROM mario_scores
				""";

		try (Connection conn = DatabaseConfig.getConnection();
				Statement stmt = conn.createStatement();
				ResultSet rs = stmt.executeQuery(selectSQL)) {

			if (rs.next()) {
				return rs.getInt("highest_score");
			}

		} catch (SQLException e) {
			System.err.println("Error retrieving highest score from database: " + e.getMessage());
			e.printStackTrace();
		}

		return 0;
	}

	public void printAllScores() {
		List<MarioScore> scores = getAllScores();

		if (scores.isEmpty()) {
			System.out.println("No scores found in database");
			return;
		}

		System.out.println("=== ALL SCORES FROM DATABASE ===");
		for (int i = 0; i < scores.size(); i++) {
			MarioScore score = scores.get(i);
			System.out.println((i + 1) + ". " + score.toString() + " (Total: " + score.getTotalScore() + ")");
		}
		System.out.println("================================");
	}
}
