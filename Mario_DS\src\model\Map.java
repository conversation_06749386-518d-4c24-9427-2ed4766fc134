package model;

import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;

import model.brick.Brick;
import model.brick.OrdinaryBrick;
import model.enemy.Enemy;
import model.hero.Fireball;
import model.hero.Mario;
import model.prize.BoostItem;
import model.prize.Coin;
import model.prize.Prize;

public class Map implements Serializable {

  private double remainingTime;
  private <PERSON> mario;
  private <PERSON> mario2;

  private ArrayList<Brick> bricks = new ArrayList<>();
  private ArrayList<Enemy> enemies = new ArrayList<>();
  private ArrayList<Brick> groundBricks = new ArrayList<>();
  private ArrayList<Prize> revealedPrizes = new ArrayList<>();
  private ArrayList<Brick> revealedBricks = new ArrayList<>();
  private ArrayList<Fireball> fireballs = new ArrayList<>();

  private EndFlag endPoint;
  private transient BufferedImage backgroundImage;
  private double bottomBorder = 720 - 96;
  private String path;

  public Map(double remainingTime, BufferedImage backgroundImage) {
    this.backgroundImage = backgroundImage;
    this.remainingTime = remainingTime;
  }

  public Map(double remainingTime, BufferedImage backgroundImage, Mario mario, Mario mario2, ArrayList<Brick> bricks,
      ArrayList<Enemy> enemies, ArrayList<Prize> prizes, ArrayList<Brick> revealedBricks, ArrayList<Fireball> fireballs,
      EndFlag endPoint, ArrayList<Brick> groundBricks, String path, double bottomBorder) {
    this.backgroundImage = backgroundImage;
    this.remainingTime = remainingTime;

    this.mario = mario;
    this.mario2 = mario2;

    this.bricks = bricks;
    this.enemies = enemies;
    this.revealedPrizes = prizes;
    this.revealedBricks = revealedBricks;
    this.fireballs = fireballs;
    this.endPoint = endPoint;
    this.groundBricks = groundBricks;
    this.path = path;
    this.bottomBorder = bottomBorder;
  }

  public Mario getMario(String whichMario) {
    if (whichMario == "mario") {
      return mario;
    } else {
      return mario2;
    }
  }

  public void setMario(Mario mario, String whichMario) {
    if (whichMario == "mario") {
      this.mario = mario;
    } else {
      this.mario2 = mario;
    }
  }

  public ArrayList<Enemy> getEnemies() {
    return enemies;
  }

  public void setEnemies(ArrayList<Enemy> enemies) {
    this.enemies = enemies;
  }

  public void syncEnemies(ArrayList<Enemy> serverEnemies) {
    // Synchronize enemy states from server while preserving local enemy objects
    // This prevents issues with transient fields and object references

    // If the number of enemies has changed, we need to replace the list
    if (this.enemies.size() != serverEnemies.size()) {
      this.enemies = serverEnemies;
      return;
    }

    // Update positions and states of existing enemies
    for (int i = 0; i < this.enemies.size() && i < serverEnemies.size(); i++) {
      Enemy localEnemy = this.enemies.get(i);
      Enemy serverEnemy = serverEnemies.get(i);

      // Update position and velocity
      localEnemy.setX(serverEnemy.getX());
      localEnemy.setY(serverEnemy.getY());
      localEnemy.setVelX(serverEnemy.getVelX());
      localEnemy.setVelY(serverEnemy.getVelY());
      localEnemy.setFalling(serverEnemy.isFalling());
      localEnemy.setJumping(serverEnemy.isJumping());
    }
  }

  public ArrayList<Fireball> getFireballs() {
    return fireballs;
  }

  public ArrayList<Prize> getRevealedPrizes() {
    return revealedPrizes;
  }

  public void setPrizes(ArrayList<Prize> prizes) {
    this.revealedPrizes = prizes;
  }

  public ArrayList<Brick> getAllBricks() {
    ArrayList<Brick> allBricks = new ArrayList<>();

    allBricks.addAll(bricks);
    allBricks.addAll(groundBricks);

    return allBricks;
  }

  public ArrayList<Brick> getBricks() {
    return bricks;
  }

  public ArrayList<Brick> getGroundBricks() {
    return groundBricks;
  }

  public ArrayList<Brick> getRevealedBricks() {
    return revealedBricks;
  }

  public void setBricks(ArrayList<Brick> bricks) {
    this.bricks = bricks;
  }

  public void addBrick(Brick brick) {
    this.bricks.add(brick);
  }

  public void addGroundBrick(Brick brick) {
    this.groundBricks.add(brick);
  }

  public void addEnemy(Enemy enemy) {
    this.enemies.add(enemy);
  }

  public void drawMap(Graphics2D g2) {
    drawBackground(g2);
    drawPrizes(g2);
    drawBricks(g2);
    drawEnemies(g2);
    drawFireballs(g2);
    drawMario(g2);
    drawMario2(g2);
    endPoint.draw(g2);
  }

  private void drawFireballs(Graphics2D g2) {
    for (Fireball fireball : fireballs) {
      fireball.draw(g2);
    }
  }

  private void drawPrizes(Graphics2D g2) {
    for (Prize prize : revealedPrizes) {
      if (prize instanceof Coin) {
        ((Coin) prize).draw(g2);
      } else if (prize instanceof BoostItem) {
        ((BoostItem) prize).draw(g2);
      }
    }
  }

  private void drawBackground(Graphics2D g2) {
    g2.drawImage(backgroundImage, 0, 0, null);
  }

  private void drawBricks(Graphics2D g2) {
    for (Brick brick : bricks) {
      if (brick != null)
        brick.draw(g2);
    }

    for (Brick brick : groundBricks) {
      brick.draw(g2);
    }
  }

  private void drawEnemies(Graphics2D g2) {
    for (Enemy enemy : enemies) {
      if (enemy != null) {
        enemy.draw(g2);
      }
    }
  }

  private void drawMario(Graphics2D g2) {
    mario.draw(g2);
  }

  private void drawMario2(Graphics2D g2) {
    mario2.draw(g2);
  }

  public void updateLocations() {
    updateLocations(true);
  }

  public void updateLocations(boolean updateEnemies) {
    mario.updateLocation();
    mario2.updateLocation();

    if (updateEnemies) {
      for (Enemy enemy : enemies) {
        enemy.updateLocation();
      }
    }

    for (Iterator<Prize> prizeIterator = revealedPrizes.iterator(); prizeIterator.hasNext();) {
      Prize prize = prizeIterator.next();
      if (prize instanceof Coin) {
        ((Coin) prize).updateLocation();
        if (((Coin) prize).getRevealBoundary() > ((Coin) prize).getY()) {
          prizeIterator.remove();
        }
      } else if (prize instanceof BoostItem) {
        ((BoostItem) prize).updateLocation();
      }
    }

    for (Fireball fireball : fireballs) {
      fireball.updateLocation();
    }

    for (Iterator<Brick> brickIterator = revealedBricks.iterator(); brickIterator.hasNext();) {
      OrdinaryBrick brick = (OrdinaryBrick) brickIterator.next();
      brick.animate();
      if (brick.getFrames() < 0) {
        bricks.remove(brick);
        brickIterator.remove();
      }
    }

    endPoint.updateLocation();
  }

  public double getBottomBorder() {
    return bottomBorder;
  }

  public void addRevealedPrize(Prize prize) {
    revealedPrizes.add(prize);
  }

  public void addFireball(Fireball fireball, String whichMario) {
    fireballs.add(fireball);
  }

  public void setEndPoint(EndFlag endPoint) {
    this.endPoint = endPoint;
  }

  public EndFlag getEndPoint() {
    return endPoint;
  }

  public void addRevealedBrick(OrdinaryBrick ordinaryBrick) {
    revealedBricks.add(ordinaryBrick);
  }

  public void removeFireball(Fireball object) {
    fireballs.remove(object);
  }

  public void removeEnemy(Enemy object) {
    enemies.remove(object);
  }

  public void removePrize(Prize object) {
    revealedPrizes.remove(object);
  }

  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public void updateTime(double passed) {
    remainingTime = remainingTime - passed;
  }

  public boolean isTimeOver() {
    return remainingTime <= 0;
  }

  public double getRemainingTime() {
    return remainingTime;
  }

  // Additional setter methods for complete map synchronization
  public void setGroundBricks(ArrayList<Brick> groundBricks) {
    this.groundBricks = groundBricks;
  }

  public void setRevealedBricks(ArrayList<Brick> revealedBricks) {
    this.revealedBricks = revealedBricks;
  }

  public void setFireballs(ArrayList<Fireball> fireballs) {
    this.fireballs = fireballs;
  }

  public void setBottomBorder(double bottomBorder) {
    this.bottomBorder = bottomBorder;
  }

  public void setRemainingTime(double remainingTime) {
    this.remainingTime = remainingTime;
  }
}
