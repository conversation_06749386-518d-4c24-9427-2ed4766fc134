package manager.network;

import java.awt.Rectangle;
import java.util.ArrayList;
import java.util.List;

import manager.Camera;
import model.GameObject;
import model.enemy.Enemy;

/**
 * Utility class for viewport-based culling to optimize network synchronization.
 * Only syncs objects that are visible on screen or within a buffer distance.
 */
public class ViewportCulling {

  /**
   * Calculate the viewport bounds including buffer area
   *
   * @param camera       The game camera
   * @param screenWidth  Screen width in pixels
   * @param screenHeight Screen height in pixels
   * @return Rectangle representing the culling area
   */
  public static Rectangle calculateCullingBounds(Camera camera, int screenWidth, int screenHeight) {
    double cameraX = camera.getX();
    double cameraY = camera.getY();

    // Expand the viewport by buffer distance in all directions
    double bufferDistance = NetworkConfig.getCullingBuffer();
    double leftBound = cameraX - bufferDistance;
    double rightBound = cameraX + screenWidth + bufferDistance;
    double topBound = cameraY - bufferDistance;
    double bottomBound = cameraY + screenHeight + bufferDistance;

    return new Rectangle((int) leftBound, (int) topBound, (int) (rightBound - leftBound),
        (int) (bottomBound - topBound));
  }

  /**
   * Check if a game object is within the culling bounds
   *
   * @param object        The game object to check
   * @param cullingBounds The viewport culling bounds
   * @return true if the object should be synced
   */
  public static boolean isObjectInCullingBounds(GameObject object, Rectangle cullingBounds) {
    if (object == null) {
      return false;
    }

    // Get object bounds
    double objX = object.getX();
    double objY = object.getY();
    double objWidth = object.getDimension() != null ? object.getDimension().width : 32; // Default
                                                                                        // size
    double objHeight = object.getDimension() != null ? object.getDimension().height : 32;

    Rectangle objectBounds = new Rectangle((int) objX, (int) objY, (int) objWidth, (int) objHeight);

    return cullingBounds.intersects(objectBounds);
  }

  /**
   * Filter enemies that are within the viewport culling bounds
   *
   * @param enemies       List of all enemies
   * @param cullingBounds The viewport culling bounds
   * @return List of enemies that should be synced
   */
  public static List<Enemy> filterEnemiesInBounds(List<Enemy> enemies, Rectangle cullingBounds) {
    List<Enemy> visibleEnemies = new ArrayList<>();

    if (enemies == null) {
      return visibleEnemies;
    }

    for (Enemy enemy : enemies) {
      if (isObjectInCullingBounds(enemy, cullingBounds)) {
        visibleEnemies.add(enemy);
      }
    }

    return visibleEnemies;
  }

  /**
   * Get the buffer distance used for culling
   *
   * @return Buffer distance in pixels
   */
  public static double getBufferDistance() {
    return NetworkConfig.getCullingBuffer();
  }

  /**
   * Log culling statistics for debugging
   *
   * @param totalObjects   Total number of objects before culling
   * @param visibleObjects Number of objects after culling
   * @param cullingBounds  The culling bounds used
   */
  public static void logCullingStats(int totalObjects, int visibleObjects, Rectangle cullingBounds) {
    if (totalObjects > 0) {
      double cullingRatio = (double) visibleObjects / totalObjects;
      double savedBandwidth = (1.0 - cullingRatio) * 100;
      System.out.println(
          String.format("\u001B[33m[Culling]\u001B[0m Enemies: %d -> %d (%.1f%% synced, %.1f%% bandwidth saved)",
              totalObjects, visibleObjects, cullingRatio * 100, savedBandwidth));
    }
  }

  /**
   * Calculate the approximate bandwidth savings from culling
   *
   * @param totalObjects   Total objects before culling
   * @param visibleObjects Objects after culling
   * @return Estimated bytes saved per sync
   */
  public static int calculateBandwidthSavings(int totalObjects, int visibleObjects) {
    int culledObjects = totalObjects - visibleObjects;
    // Estimate ~50 bytes per enemy state (position, velocity, flags, etc.)
    return culledObjects * 50;
  }
}
