package manager.network;

import java.io.*;
import java.net.*;
import java.util.concurrent.TimeUnit;

import manager.network.NetworkMessage.PlayerState;
import model.hero.Mario;

/**
 * Utility class for common network operations and helper methods.
 * Provides functionality for network validation, state conversion, and connection testing.
 */
public class NetworkUtils {
    
    private static final int DEFAULT_TIMEOUT_MS = 5000;
    private static final int MAX_PACKET_SIZE = 8192; // 8KB max packet size
    
    /**
     * Test if a host is reachable on a specific port
     */
    public static boolean isHostReachable(String host, int port, int timeoutMs) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), timeoutMs);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
    
    /**
     * Test if a host is reachable with default timeout
     */
    public static boolean isHostReachable(String host, int port) {
        return isHostReachable(host, port, DEFAULT_TIMEOUT_MS);
    }
    
    /**
     * Validate IP address format
     */
    public static boolean isValidIPAddress(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        
        // Allow localhost
        if ("localhost".equalsIgnoreCase(ip.trim())) {
            return true;
        }
        
        try {
            InetAddress.getByName(ip);
            return true;
        } catch (UnknownHostException e) {
            return false;
        }
    }
    
    /**
     * Validate port number
     */
    public static boolean isValidPort(int port) {
        return port >= 1024 && port <= 65535; // Avoid system ports
    }
    
    /**
     * Convert Mario object to PlayerState for network transmission
     */
    public static PlayerState marioToPlayerState(Mario mario) {
        if (mario == null) {
            return null;
        }
        
        return new PlayerState(
            mario.getX(),
            mario.getY(),
            mario.getVelX(),
            mario.getVelY(),
            mario.isJumping(),
            mario.isFalling(),
            mario.getToRight(),
            mario.getRemainingLives(),
            mario.getPoints(),
            mario.getCoins()
        );
    }
    
    /**
     * Apply PlayerState to Mario object (for network synchronization)
     */
    public static void applyPlayerStateToMario(PlayerState state, Mario mario) {
        if (state == null || mario == null) {
            return;
        }
        
        mario.setX(state.getX());
        mario.setY(state.getY());
        mario.setVelX(state.getVelX());
        mario.setVelY(state.getVelY());
        mario.setJumping(state.isJumping());
        mario.setFalling(state.isFalling());
        mario.setPoints(state.getPoints());
        mario.setRemainingLives(state.getLives());
        // Note: Coins are handled separately as they're cumulative
    }
    
    /**
     * Calculate network latency between two timestamps
     */
    public static long calculateLatency(long sendTime, long receiveTime) {
        return Math.max(0, receiveTime - sendTime);
    }
    
    /**
     * Format latency for display
     */
    public static String formatLatency(long latencyMs) {
        if (latencyMs < 50) {
            return "Good (" + latencyMs + "ms)";
        } else if (latencyMs < 100) {
            return "Fair (" + latencyMs + "ms)";
        } else if (latencyMs < 200) {
            return "Poor (" + latencyMs + "ms)";
        } else {
            return "Bad (" + latencyMs + "ms)";
        }
    }
    
    /**
     * Serialize a network message to byte array
     */
    public static byte[] serializeMessage(NetworkMessage message) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ObjectOutputStream oos = new ObjectOutputStream(baos)) {
            
            oos.writeObject(message);
            oos.flush();
            
            byte[] data = baos.toByteArray();
            if (data.length > MAX_PACKET_SIZE) {
                throw new IOException("Message too large: " + data.length + " bytes");
            }
            
            return data;
        }
    }
    
    /**
     * Deserialize byte array to network message
     */
    public static NetworkMessage deserializeMessage(byte[] data) throws IOException, ClassNotFoundException {
        if (data.length > MAX_PACKET_SIZE) {
            throw new IOException("Packet too large: " + data.length + " bytes");
        }
        
        try (ByteArrayInputStream bais = new ByteArrayInputStream(data);
             ObjectInputStream ois = new ObjectInputStream(bais)) {
            
            return (NetworkMessage) ois.readObject();
        }
    }
    
    /**
     * Get local IP address for hosting
     */
    public static String getLocalIPAddress() {
        try {
            // Try to get the actual network interface IP
            for (NetworkInterface ni : java.util.Collections.list(NetworkInterface.getNetworkInterfaces())) {
                if (!ni.isLoopback() && ni.isUp()) {
                    for (InetAddress addr : java.util.Collections.list(ni.getInetAddresses())) {
                        if (addr instanceof Inet4Address && !addr.isLoopbackAddress()) {
                            return addr.getHostAddress();
                        }
                    }
                }
            }
        } catch (SocketException e) {
            System.err.println("Error getting local IP: " + e.getMessage());
        }
        
        // Fallback to localhost
        return "127.0.0.1";
    }
    
    /**
     * Create a connection timeout for sockets
     */
    public static void setSocketTimeout(Socket socket, int timeoutMs) throws SocketException {
        socket.setSoTimeout(timeoutMs);
        socket.setTcpNoDelay(true); // Disable Nagle's algorithm for low latency
        socket.setKeepAlive(true);  // Enable keep-alive
    }
    
    /**
     * Safe close for network resources
     */
    public static void safeClose(Closeable... resources) {
        for (Closeable resource : resources) {
            if (resource != null) {
                try {
                    resource.close();
                } catch (IOException e) {
                    // Log but don't throw - we're cleaning up
                    System.err.println("Error closing resource: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * Check if a network error is recoverable
     */
    public static boolean isRecoverableNetworkError(Throwable error) {
        if (error instanceof SocketTimeoutException) {
            return true; // Timeout can be temporary
        }
        
        if (error instanceof ConnectException) {
            return true; // Connection refused might be temporary
        }
        
        if (error instanceof IOException) {
            String message = error.getMessage();
            if (message != null) {
                message = message.toLowerCase();
                // These are typically temporary network issues
                return message.contains("connection reset") ||
                       message.contains("broken pipe") ||
                       message.contains("network is unreachable");
            }
        }
        
        return false; // Other errors are likely permanent
    }
    
    /**
     * Calculate exponential backoff delay for reconnection attempts
     */
    public static long calculateBackoffDelay(int attemptNumber, long baseDelayMs, long maxDelayMs) {
        long delay = baseDelayMs * (1L << Math.min(attemptNumber, 10)); // Cap at 2^10
        return Math.min(delay, maxDelayMs);
    }
    
    /**
     * Validate game version compatibility
     */
    public static boolean isVersionCompatible(String clientVersion, String serverVersion) {
        if (clientVersion == null || serverVersion == null) {
            return false;
        }
        
        // For now, require exact version match
        // In the future, could implement semantic versioning compatibility
        return clientVersion.equals(serverVersion);
    }
    
    /**
     * Generate a simple connection ID for tracking
     */
    public static String generateConnectionId() {
        return "conn_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
}
