package test;

import java.awt.Rectangle;
import java.util.ArrayList;
import java.util.List;

import manager.Camera;
import manager.network.ViewportCulling;
import model.enemy.Enemy;

/**
 * Test viewport culling optimization for network synchronization
 */
public class ViewportCullingTest {

  public static void main(String[] args) {
    System.out.println("=== Viewport Culling Test ===");

    // Test 1: Basic culling bounds calculation
    System.out.println("\n--- Test 1: Culling Bounds Calculation ---");
    Camera camera = new Camera();
    camera.setX(500);
    camera.setY(300); // Camera at (500, 300)
    int screenWidth = 800;
    int screenHeight = 600;

    Rectangle bounds = ViewportCulling.calculateCullingBounds(camera, screenWidth, screenHeight);
    System.out.println("Camera position: (" + camera.getX() + ", " + camera.getY() + ")");
    System.out.println("Screen size: " + screenWidth + "x" + screenHeight);
    System.out
        .println("Culling bounds: [" + bounds.x + "," + bounds.y + "," + bounds.width + "x" + bounds.height + "]");
    System.out.println("Buffer distance: " + ViewportCulling.getBufferDistance());

    // Test 2: Object visibility testing
    System.out.println("\n--- Test 2: Object Visibility Testing ---");

    // Create test enemies at different positions
    List<Enemy> enemies = new ArrayList<>();
    enemies.add(new MockEnemy(400, 250)); // Visible (in viewport)
    enemies.add(new MockEnemy(600, 400)); // Visible (in viewport)
    enemies.add(new MockEnemy(100, 100)); // Not visible (too far left)
    enemies.add(new MockEnemy(1500, 300)); // Not visible (too far right)
    enemies.add(new MockEnemy(500, 1200)); // Not visible (too far down)
    enemies.add(new MockEnemy(500, -100)); // Not visible (too far up)
    enemies.add(new MockEnemy(300, 300)); // Visible (within buffer)
    enemies.add(new MockEnemy(1300, 300)); // Visible (within buffer)

    System.out.println("Total enemies: " + enemies.size());
    for (int i = 0; i < enemies.size(); i++) {
      Enemy enemy = enemies.get(i);
      boolean visible = ViewportCulling.isObjectInCullingBounds(enemy, bounds);
      System.out.println(
          "Enemy " + i + " at (" + enemy.getX() + "," + enemy.getY() + "): " + (visible ? "VISIBLE" : "CULLED"));
    }

    // Test 3: Enemy filtering
    System.out.println("\n--- Test 3: Enemy Filtering ---");
    List<Enemy> visibleEnemies = ViewportCulling.filterEnemiesInBounds(enemies, bounds);
    System.out.println("Visible enemies: " + visibleEnemies.size() + "/" + enemies.size());

    ViewportCulling.logCullingStats(enemies.size(), visibleEnemies.size(), bounds);

    int bandwidthSaved = ViewportCulling.calculateBandwidthSavings(enemies.size(), visibleEnemies.size());
    System.out.println("Estimated bandwidth saved: " + bandwidthSaved + " bytes/sync");

    // Test 4: Edge cases
    System.out.println("\n--- Test 4: Edge Cases ---");

    // Test with null objects
    List<Enemy> enemiesWithNull = new ArrayList<>();
    enemiesWithNull.add(new MockEnemy(500, 300));
    enemiesWithNull.add(null);
    enemiesWithNull.add(new MockEnemy(600, 400));

    List<Enemy> filteredWithNull = ViewportCulling.filterEnemiesInBounds(enemiesWithNull, bounds);
    System.out.println("Enemies with null: " + enemiesWithNull.size() + " -> " + filteredWithNull.size());

    // Test with empty list
    List<Enemy> emptyEnemies = new ArrayList<>();
    List<Enemy> filteredEmpty = ViewportCulling.filterEnemiesInBounds(emptyEnemies, bounds);
    System.out.println("Empty list: " + emptyEnemies.size() + " -> " + filteredEmpty.size());

    // Test 5: Performance simulation
    System.out.println("\n--- Test 5: Performance Simulation ---");

    // Simulate a large map with many enemies
    List<Enemy> manyEnemies = new ArrayList<>();
    for (int i = 0; i < 100; i++) {
      // Spread enemies across a large map (0-3000 x 0-1000)
      double x = Math.random() * 3000;
      double y = Math.random() * 1000;
      manyEnemies.add(new MockEnemy(x, y));
    }

    long startTime = System.nanoTime();
    List<Enemy> visibleMany = ViewportCulling.filterEnemiesInBounds(manyEnemies, bounds);
    long endTime = System.nanoTime();

    double processingTime = (endTime - startTime) / 1_000_000.0; // Convert to
                                                                 // milliseconds

    System.out.println("Large map simulation:");
    System.out.println("Total enemies: " + manyEnemies.size());
    System.out.println("Visible enemies: " + visibleMany.size());
    System.out.println("Processing time: " + String.format("%.3f", processingTime) + " ms");

    ViewportCulling.logCullingStats(manyEnemies.size(), visibleMany.size(), bounds);

    int largeBandwidthSaved = ViewportCulling.calculateBandwidthSavings(manyEnemies.size(), visibleMany.size());
    System.out.println("Bandwidth saved: " + largeBandwidthSaved + " bytes/sync");
    System.out.println("Bandwidth saved per second (30 FPS): " + (largeBandwidthSaved * 30) + " bytes/sec");

    System.out.println("\n=== All Tests Completed ===");
  }

  static class MockEnemy extends Enemy {
    public MockEnemy(double x, double y) {
      super(x, y, null);
      setDimension(32, 32); // Standard enemy size
    }

    @Override
    public void updateLocation() {
      // Mock implementation
    }
  }
}
