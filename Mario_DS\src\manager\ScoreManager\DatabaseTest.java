package manager.ScoreManager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;

public class DatabaseTest {

	public static void main(String[] args) {
		System.out.println("=== Testing PostgreSQL Connection ===");

		try {
			// Test database connection and table creation
			System.out.println("Testing database connection...");

			// Create a score repository to test database operations
			ScoreRepository repository = new ScoreRepository();

			// Test inserting a sample score directly
			System.out.println("Testing score insertion...");
			insertTestScore(1500, 2300);

			// Test retrieving scores
			System.out.println("Testing score retrieval...");
			List<MarioScore> scores = repository.getAllScores();
			System.out.println("Found " + scores.size() + " scores in database");

			// Print all scores
			if (!scores.isEmpty()) {
				repository.printAllScores();
			}

			// Test getting highest score
			int highestScore = repository.getHighestScore();
			System.out.println("Highest score: " + highestScore);

			// Test getting top scores
			List<MarioScore> topScores = repository.getTopScores(5);
			System.out.println("Top 5 scores: " + topScores.size() + " found");

			System.out.println("=== Database test completed successfully! ===");

		} catch (Exception e) {
			System.err.println("Database test failed: " + e.getMessage());
			e.printStackTrace();
		} finally {
			// Close database connection
			DatabaseConfig.closeConnection();
		}
	}

	private static void insertTestScore(int marioScore, int mario2Score) {
		String insertSQL = """
				INSERT INTO mario_scores (mario_score, mario2_score, total_score, game_date)
				VALUES (?, ?, ?, ?)
				""";

		try (Connection conn = DatabaseConfig.getConnection();
				PreparedStatement pstmt = conn.prepareStatement(insertSQL)) {

			int totalScore = marioScore + mario2Score;
			LocalDateTime now = LocalDateTime.now();

			pstmt.setInt(1, marioScore);
			pstmt.setInt(2, mario2Score);
			pstmt.setInt(3, totalScore);
			pstmt.setObject(4, now);

			int rowsAffected = pstmt.executeUpdate();

			if (rowsAffected > 0) {
				System.out.println("Test score inserted successfully!");
				System.out.println("Mario: " + marioScore + " - Mario2: " + mario2Score +
						" - Total: " + totalScore + " - Date: " + now);
			} else {
				System.err.println("Failed to insert test score");
			}

		} catch (SQLException e) {
			System.err.println("Error inserting test score: " + e.getMessage());
			e.printStackTrace();
		}
	}
}
