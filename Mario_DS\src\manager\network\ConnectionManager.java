package manager.network;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import manager.ButtonAction;
import manager.MultiplayerManager;

/**
 * Manages network connections with automatic reconnection, timeout handling,
 * and connection state monitoring for both client and server modes.
 */
public class ConnectionManager {

  private static final int MAX_RECONNECT_ATTEMPTS = 5;
  private static final long BASE_RECONNECT_DELAY_MS = 1000; // 1 second
  private static final long MAX_RECONNECT_DELAY_MS = 30000; // 30 seconds
  private static final long CONNECTION_HEALTH_CHECK_INTERVAL_MS = 10000; // 10
                                                                         // seconds

  private final MultiplayerManager multiplayerManager;
  private final ScheduledExecutorService scheduler;

  private AtomicBoolean isConnected;
  private AtomicBoolean isReconnecting;
  private AtomicInteger reconnectAttempts;
  private AtomicBoolean shouldReconnect;

  private NetworkServer networkServer;
  private NetworkClient networkClient;

  private String lastServerAddress;
  private int lastServerPort;
  private ConnectionState currentState;

  public enum ConnectionState {
    DISCONNECTED, CONNECTING, CONNECTED, RECONNECTING, FAILED
  }

  public ConnectionManager(MultiplayerManager multiplayerManager) {
    this.multiplayerManager = multiplayerManager;
    this.scheduler = Executors.newScheduledThreadPool(2);
    this.isConnected = new AtomicBoolean(false);
    this.isReconnecting = new AtomicBoolean(false);
    this.reconnectAttempts = new AtomicInteger(0);
    this.shouldReconnect = new AtomicBoolean(true);
    this.currentState = ConnectionState.DISCONNECTED;

    // Start connection health monitoring
    startHealthMonitoring();
  }

  /**
   * Start hosting a server
   */
  public boolean startServer(int port) {
    try {
      setState(ConnectionState.CONNECTING);

      networkServer = new NetworkServer(multiplayerManager, port);
      networkServer.start();

      lastServerPort = port;
      setState(ConnectionState.CONNECTED);
      onConnectionEstablished();

      System.out.println("Server started successfully on port " + port);
      return true;

    } catch (Exception e) {
      System.err.println("Failed to start server: " + e.getMessage());
      setState(ConnectionState.FAILED);
      return false;
    }
  }

  /**
   * Connect to a server as client
   */
  public boolean connectToServer(String address, int port) {
    if (isConnected.get() || isReconnecting.get()) {
      return false;
    }

    lastServerAddress = address;
    lastServerPort = port;

    return attemptConnection();
  }

  /**
   * Attempt to establish connection
   */
  private boolean attemptConnection() {
    try {
      setState(ConnectionState.CONNECTING);

      // Validate connection parameters
      if (!NetworkUtils.isValidIPAddress(lastServerAddress)) {
        throw new IllegalArgumentException("Invalid server address: " + lastServerAddress);
      }

      if (!NetworkUtils.isValidPort(lastServerPort)) {
        throw new IllegalArgumentException("Invalid port: " + lastServerPort);
      }

      // Test if host is reachable
      if (!NetworkUtils.isHostReachable(lastServerAddress, lastServerPort, 5000)) {
        throw new RuntimeException("Server is not reachable");
      }

      networkClient = new NetworkClient(multiplayerManager, lastServerAddress, lastServerPort);
      networkClient.connect();

      // Wait a moment for connection to establish
      Thread.sleep(1000);

      if (networkClient.isConnected()) {
        setState(ConnectionState.CONNECTED);
        onConnectionEstablished();
        return true;
      } else {
        throw new RuntimeException("Connection failed");
      }

    } catch (Exception e) {
      System.err.println("Connection attempt failed: " + e.getMessage());
      setState(ConnectionState.FAILED);
      scheduleReconnect();
      return false;
    }
  }

  /**
   * Disconnect from current connection
   */
  public void disconnect() {
    shouldReconnect.set(false);
    isReconnecting.set(false);

    if (networkServer != null) {
      networkServer.stop();
      networkServer = null;
    }

    if (networkClient != null) {
      networkClient.disconnect();
      networkClient = null;
    }

    setState(ConnectionState.DISCONNECTED);
    onConnectionLost();
  }

  /**
   * Schedule automatic reconnection
   */
  private void scheduleReconnect() {
    if (!shouldReconnect.get() || isReconnecting.get()) {
      return;
    }

    int attempts = reconnectAttempts.incrementAndGet();
    if (attempts > MAX_RECONNECT_ATTEMPTS) {
      System.err.println("Max reconnection attempts reached. Giving up.");
      setState(ConnectionState.FAILED);
      return;
    }

    long delay = NetworkUtils.calculateBackoffDelay(attempts, BASE_RECONNECT_DELAY_MS, MAX_RECONNECT_DELAY_MS);

    setState(ConnectionState.RECONNECTING);
    isReconnecting.set(true);

    System.out.println("Scheduling reconnection attempt " + attempts + " in " + delay + "ms");

    scheduler.schedule(() -> {
      if (shouldReconnect.get() && !isConnected.get()) {
        System.out.println("Attempting reconnection...");
        if (attemptConnection()) {
          reconnectAttempts.set(0); // Reset on successful connection
        }
      }
      isReconnecting.set(false);
    }, delay, TimeUnit.MILLISECONDS);
  }

  /**
   * Start monitoring connection health
   */
  private void startHealthMonitoring() {
    scheduler.scheduleAtFixedRate(() -> {
      if (isConnected.get()) {
        checkConnectionHealth();
      }
    }, CONNECTION_HEALTH_CHECK_INTERVAL_MS, CONNECTION_HEALTH_CHECK_INTERVAL_MS, TimeUnit.MILLISECONDS);
  }

  /**
   * Check if the current connection is healthy
   */
  private void checkConnectionHealth() {
    try {
      if (networkClient != null && !networkClient.isConnected()) {
        System.out.println("Client connection health check failed");
        onConnectionLost();
      }

      if (networkServer != null) {
        // Server health is checked by individual client handlers
        // Could add additional server health checks here
      }

    } catch (Exception e) {
      System.err.println("Connection health check error: " + e.getMessage());
      onConnectionLost();
    }
  }

  /**
   * Handle successful connection establishment
   */
  private void onConnectionEstablished() {
    isConnected.set(true);
    reconnectAttempts.set(0);
    shouldReconnect.set(true);
    multiplayerManager.onConnectionEstablished();
  }

  /**
   * Handle connection loss
   */
  private void onConnectionLost() {
    if (isConnected.getAndSet(false)) {
      multiplayerManager.onConnectionLost();

      // Only attempt reconnection for client connections
      if (networkClient != null && shouldReconnect.get()) {
        scheduleReconnect();
      }
    }
  }

  /**
   * Set the current connection state
   */
  private void setState(ConnectionState state) {
    this.currentState = state;
    System.out.println("Connection state changed to: " + state);
  }

  /**
   * Get current connection state
   */
  public ConnectionState getState() {
    return currentState;
  }

  /**
   * Check if currently connected
   */
  public boolean isConnected() {
    return isConnected.get();
  }

  /**
   * Check if currently attempting to reconnect
   */
  public boolean isReconnecting() {
    return isReconnecting.get();
  }

  /**
   * Get the number of reconnection attempts made
   */
  public int getReconnectAttempts() {
    return reconnectAttempts.get();
  }

  /**
   * Get connection info string for UI display
   */
  public String getConnectionInfo() {
    switch (currentState) {
    case DISCONNECTED:
      return "Disconnected";
    case CONNECTING:
      return "Connecting...";
    case CONNECTED:
      if (networkServer != null) {
        return "Hosting on port " + lastServerPort;
      } else if (networkClient != null) {
        return "Connected to " + lastServerAddress + ":" + lastServerPort;
      }
      return "Connected";
    case RECONNECTING:
      return "Reconnecting... (attempt " + reconnectAttempts.get() + "/" + MAX_RECONNECT_ATTEMPTS + ")";
    case FAILED:
      return "Connection failed";
    default:
      return "Unknown";
    }
  }

  /**
   * Get the network server instance (if hosting)
   */
  public NetworkServer getNetworkServer() {
    return networkServer;
  }

  /**
   * Get the network client instance (if connected as client)
   */
  public NetworkClient getNetworkClient() {
    return networkClient;
  }

  /**
   * Send player input through the appropriate network component
   */
  public void sendPlayerInput(String playerId, ButtonAction action, long timestamp) {
    if (!isConnected.get()) {
      return;
    }

    if (networkServer != null) {
      // Server broadcasts to clients
      networkServer.broadcastPlayerInput(playerId, action, timestamp);
    } else if (networkClient != null) {
      // Client sends to server
      networkClient.sendPlayerInput(playerId, action, timestamp);
    }
  }

  /**
   * Cleanup resources
   */
  public void cleanup() {
    shouldReconnect.set(false);
    disconnect();

    scheduler.shutdown();
    try {
      if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
        scheduler.shutdownNow();
      }
    } catch (InterruptedException e) {
      scheduler.shutdownNow();
    }
  }
}
