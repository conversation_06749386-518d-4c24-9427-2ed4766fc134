package manager;

/**
 * Simple test runner for multiplayer functionality.
 * Can be used to validate the implementation during development.
 */
public class TestRunner {
    
    /**
     * Run multiplayer tests with a mock game engine
     */
    public static void main(String[] args) {
        System.out.println("Mario DS Multiplayer Test Runner");
        System.out.println("================================");
        
        try {
            // Create a minimal game engine for testing
            GameEngine testEngine = createTestGameEngine();
            
            // Run the tests
            MultiplayerTester tester = new MultiplayerTester(testEngine);
            MultiplayerTester.TestResults results = tester.runAllTests();
            
            // Exit with appropriate code
            System.exit(results.allPassed() ? 0 : 1);
            
        } catch (Exception e) {
            System.err.println("Test runner failed: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    /**
     * Create a minimal game engine for testing purposes
     */
    private static GameEngine createTestGameEngine() {
        // Note: This would need to be adapted based on GameEngine constructor requirements
        // For now, we'll assume the GameEngine can be created with minimal parameters
        
        try {
            // Create game engine with default parameters
            GameEngine engine = new GameEngine();
            
            System.out.println("Test game engine created successfully");
            return engine;
            
        } catch (Exception e) {
            System.err.println("Failed to create test game engine: " + e.getMessage());
            throw new RuntimeException("Cannot create test environment", e);
        }
    }
    
    /**
     * Run a quick validation of core multiplayer components
     */
    public static boolean quickValidation() {
        System.out.println("Running quick multiplayer validation...");
        
        try {
            // Test 1: GameMode enum
            for (GameMode mode : GameMode.values()) {
                System.out.println("✓ GameMode." + mode + " available");
            }
            
            // Test 2: Network utilities
            if (manager.network.NetworkUtils.isValidIPAddress("localhost")) {
                System.out.println("✓ NetworkUtils working");
            } else {
                System.err.println("✗ NetworkUtils validation failed");
                return false;
            }
            
            // Test 3: Button actions
            for (ButtonAction action : ButtonAction.values()) {
                if (action.toString().length() > 0) {
                    // Basic validation that enum values exist
                }
            }
            System.out.println("✓ ButtonAction enum available");
            
            System.out.println("Quick validation passed!");
            return true;
            
        } catch (Exception e) {
            System.err.println("Quick validation failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Print system information relevant to multiplayer functionality
     */
    public static void printSystemInfo() {
        System.out.println("\n=== System Information ===");
        
        // Java version
        System.out.println("Java Version: " + System.getProperty("java.version"));
        
        // Operating System
        System.out.println("OS: " + System.getProperty("os.name") + " " + System.getProperty("os.version"));
        
        // Available processors (for threading)
        System.out.println("Available Processors: " + Runtime.getRuntime().availableProcessors());
        
        // Memory information
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        
        System.out.println("Max Memory: " + (maxMemory / 1024 / 1024) + " MB");
        System.out.println("Total Memory: " + (totalMemory / 1024 / 1024) + " MB");
        System.out.println("Free Memory: " + (freeMemory / 1024 / 1024) + " MB");
        
        // Network information
        try {
            String localIP = manager.network.NetworkUtils.getLocalIPAddress();
            System.out.println("Local IP Address: " + localIP);
        } catch (Exception e) {
            System.out.println("Local IP Address: Unable to determine (" + e.getMessage() + ")");
        }
        
        System.out.println("=========================\n");
    }
    
    /**
     * Test network connectivity
     */
    public static boolean testNetworkConnectivity() {
        System.out.println("Testing network connectivity...");
        
        try {
            // Test localhost connectivity
            boolean localhostReachable = manager.network.NetworkUtils.isHostReachable("localhost", 12345, 1000);
            System.out.println("Localhost reachable: " + localhostReachable);
            
            // Test port validation
            boolean validPort = manager.network.NetworkUtils.isValidPort(12345);
            System.out.println("Port 12345 valid: " + validPort);
            
            boolean invalidPort = !manager.network.NetworkUtils.isValidPort(80); // System port should be invalid
            System.out.println("Port 80 correctly rejected: " + invalidPort);
            
            return validPort && invalidPort;
            
        } catch (Exception e) {
            System.err.println("Network connectivity test failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Comprehensive test suite
     */
    public static void runComprehensiveTests() {
        System.out.println("=== Comprehensive Multiplayer Test Suite ===\n");
        
        // Print system information
        printSystemInfo();
        
        // Quick validation
        boolean quickValid = quickValidation();
        System.out.println("Quick Validation: " + (quickValid ? "PASS" : "FAIL"));
        
        // Network connectivity
        boolean networkValid = testNetworkConnectivity();
        System.out.println("Network Connectivity: " + (networkValid ? "PASS" : "FAIL"));
        
        // Full multiplayer tests (if basic tests pass)
        if (quickValid && networkValid) {
            try {
                GameEngine testEngine = createTestGameEngine();
                MultiplayerTester tester = new MultiplayerTester(testEngine);
                MultiplayerTester.TestResults results = tester.runAllTests();
                
                System.out.println("\n=== Final Results ===");
                System.out.println("All Tests: " + (results.allPassed() ? "PASS" : "FAIL"));
                
            } catch (Exception e) {
                System.err.println("Full test suite failed: " + e.getMessage());
            }
        } else {
            System.err.println("Skipping full tests due to basic validation failures");
        }
        
        System.out.println("\n=== Test Suite Complete ===");
    }
}
