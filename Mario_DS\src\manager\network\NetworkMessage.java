package manager.network;

import java.io.Serializable;

import manager.ButtonAction;

/**
 * Base class for all network messages exchanged between client and server. All
 * messages are serializable for transmission over TCP sockets.
 */
public abstract class NetworkMessage implements Serializable {

  private static final long serialVersionUID = 1L;

  protected final MessageType type;
  protected final long timestamp;

  public NetworkMessage(MessageType type) {
    this.type = type;
    this.timestamp = System.currentTimeMillis();
  }

  public MessageType getType() {
    return type;
  }

  public long getTimestamp() {
    return timestamp;
  }

  /**
   * Enumeration of all message types
   */
  public enum MessageType {
    PLAYER_INPUT, GAME_STATE, CONNECTION_REQUEST, CONNECTION_RESPONSE, DISCONNECT, PING, PONG, SYNC_REQUEST,
    SYNC_RESPONSE, STATUS_SYNC
  }

  /**
   * Player input message for transmitting button actions
   */
  public static class PlayerInputMessage extends NetworkMessage {
    private final String playerId;
    private final ButtonAction action;
    private final boolean isPressed;

    public PlayerInputMessage(String playerId, ButtonAction action, boolean isPressed) {
      super(MessageType.PLAYER_INPUT);
      this.playerId = playerId;
      this.action = action;
      this.isPressed = isPressed;
    }

    public String getPlayerId() {
      return playerId;
    }

    public ButtonAction getAction() {
      return action;
    }

    public boolean isPressed() {
      return isPressed;
    }

    @Override
    public String toString() {
      return String.format("PlayerInputMessage{playerId='%s', action=%s, pressed=%s, timestamp=%d}", playerId, action,
          isPressed, timestamp);
    }
  }

  /**
   * Game state synchronization message
   */
  public static class GameStateMessage extends NetworkMessage {
    private final PlayerState mario1State;
    private final PlayerState mario2State;
    private final int remainingTime;
    private final boolean gameRunning;
    private final java.util.List<EnemyState> enemyStates;

    public GameStateMessage(PlayerState mario1State, PlayerState mario2State, int remainingTime, boolean gameRunning,
        java.util.List<EnemyState> enemyStates) {
      super(MessageType.GAME_STATE);
      this.mario1State = mario1State;
      this.mario2State = mario2State;
      this.remainingTime = remainingTime;
      this.gameRunning = gameRunning;
      this.enemyStates = enemyStates;
    }

    // Backward compatibility constructor
    public GameStateMessage(PlayerState mario1State, PlayerState mario2State, int remainingTime, boolean gameRunning) {
      this(mario1State, mario2State, remainingTime, gameRunning, new java.util.ArrayList<>());
    }

    public PlayerState getMario1State() {
      return mario1State;
    }

    public PlayerState getMario2State() {
      return mario2State;
    }

    public int getRemainingTime() {
      return remainingTime;
    }

    public boolean isGameRunning() {
      return gameRunning;
    }

    public java.util.List<EnemyState> getEnemyStates() {
      return enemyStates;
    }
  }

  /**
   * Connection request message
   */
  public static class ConnectionRequestMessage extends NetworkMessage {
    private final String playerName;
    private final String gameVersion;

    public ConnectionRequestMessage(String playerName, String gameVersion) {
      super(MessageType.CONNECTION_REQUEST);
      this.playerName = playerName;
      this.gameVersion = gameVersion;
    }

    public String getPlayerName() {
      return playerName;
    }

    public String getGameVersion() {
      return gameVersion;
    }
  }

  /**
   * Connection response message
   */
  public static class ConnectionResponseMessage extends NetworkMessage {
    private final boolean accepted;
    private final String reason;
    private final String assignedPlayerId;

    public ConnectionResponseMessage(boolean accepted, String reason, String assignedPlayerId) {
      super(MessageType.CONNECTION_RESPONSE);
      this.accepted = accepted;
      this.reason = reason;
      this.assignedPlayerId = assignedPlayerId;
    }

    public boolean isAccepted() {
      return accepted;
    }

    public String getReason() {
      return reason;
    }

    public String getAssignedPlayerId() {
      return assignedPlayerId;
    }
  }

  /**
   * Disconnect message
   */
  public static class DisconnectMessage extends NetworkMessage {
    private final String reason;

    public DisconnectMessage(String reason) {
      super(MessageType.DISCONNECT);
      this.reason = reason;
    }

    public String getReason() {
      return reason;
    }
  }

  /**
   * Ping message for connection testing
   */
  public static class PingMessage extends NetworkMessage {
    private final long pingId;

    public PingMessage(long pingId) {
      super(MessageType.PING);
      this.pingId = pingId;
    }

    public long getPingId() {
      return pingId;
    }
  }

  /**
   * Pong response message
   */
  public static class PongMessage extends NetworkMessage {
    private final long pingId;

    public PongMessage(long pingId) {
      super(MessageType.PONG);
      this.pingId = pingId;
    }

    public long getPingId() {
      return pingId;
    }
  }

  /**
   * Status synchronization message
   */
  public static class StatusSyncMessage extends NetworkMessage {
    private final String gameStatus;
    private final int selectedMap;

    public StatusSyncMessage(String gameStatus, int selectedMap) {
      super(MessageType.STATUS_SYNC);
      this.gameStatus = gameStatus;
      this.selectedMap = selectedMap;
    }

    public String getGameStatus() {
      return gameStatus;
    }

    public int getSelectedMap() {
      return selectedMap;
    }
  }

  /**
   * Represents the state of an enemy character
   */
  public static class EnemyState implements Serializable {
    private final double x, y;
    private final double velX, velY;
    private final boolean falling, jumping;
    private final String enemyType;
    private final int enemyId;

    public EnemyState(int enemyId, String enemyType, double x, double y, double velX, double velY, boolean falling,
        boolean jumping) {
      this.enemyId = enemyId;
      this.enemyType = enemyType;
      this.x = x;
      this.y = y;
      this.velX = velX;
      this.velY = velY;
      this.falling = falling;
      this.jumping = jumping;
    }

    // Getters
    public int getEnemyId() {
      return enemyId;
    }

    public String getEnemyType() {
      return enemyType;
    }

    public double getX() {
      return x;
    }

    public double getY() {
      return y;
    }

    public double getVelX() {
      return velX;
    }

    public double getVelY() {
      return velY;
    }

    public boolean isFalling() {
      return falling;
    }

    public boolean isJumping() {
      return jumping;
    }
  }

  /**
   * Represents the state of a player character
   */
  public static class PlayerState implements Serializable {
    private final double x, y;
    private final double velX, velY;
    private final boolean jumping, falling;
    private final boolean toRight;
    private final int lives, points, coins;

    public PlayerState(double x, double y, double velX, double velY, boolean jumping, boolean falling, boolean toRight,
        int lives, int points, int coins) {
      this.x = x;
      this.y = y;
      this.velX = velX;
      this.velY = velY;
      this.jumping = jumping;
      this.falling = falling;
      this.toRight = toRight;
      this.lives = lives;
      this.points = points;
      this.coins = coins;
    }

    // Getters
    public double getX() {
      return x;
    }

    public double getY() {
      return y;
    }

    public double getVelX() {
      return velX;
    }

    public double getVelY() {
      return velY;
    }

    public boolean isJumping() {
      return jumping;
    }

    public boolean isFalling() {
      return falling;
    }

    public boolean isToRight() {
      return toRight;
    }

    public int getLives() {
      return lives;
    }

    public int getPoints() {
      return points;
    }

    public int getCoins() {
      return coins;
    }
  }
}
