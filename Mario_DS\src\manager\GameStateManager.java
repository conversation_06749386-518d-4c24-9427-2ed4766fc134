package manager;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import manager.network.NetworkMessage.PlayerState;
import manager.network.NetworkUtils;
import model.hero.Mario;

/**
 * Manages game state synchronization for multiplayer games.
 * Handles server authority, client prediction, and state reconciliation.
 */
public class GameStateManager {
    
    private static final long SYNC_INTERVAL_MS = 16; // ~60 FPS
    private static final long STATE_TIMEOUT_MS = 5000; // 5 seconds
    private static final int MAX_PREDICTION_FRAMES = 10;
    
    private final GameEngine gameEngine;
    private final MultiplayerManager multiplayerManager;
    
    // State tracking
    private AtomicLong lastSyncTime;
    private AtomicLong stateSequenceNumber;
    private ConcurrentHashMap<String, PlayerStateHistory> playerStates;
    
    // Prediction and reconciliation
    private boolean enableClientPrediction;
    private boolean isAuthoritative;
    
    public GameStateManager(GameEngine gameEngine, MultiplayerManager multiplayerManager) {
        this.gameEngine = gameEngine;
        this.multiplayerManager = multiplayerManager;
        this.lastSyncTime = new AtomicLong(0);
        this.stateSequenceNumber = new AtomicLong(0);
        this.playerStates = new ConcurrentHashMap<>();
        this.enableClientPrediction = true;
        this.isAuthoritative = false;
    }
    
    /**
     * Set whether this instance has authority over game state
     */
    public void setAuthoritative(boolean authoritative) {
        this.isAuthoritative = authoritative;
        System.out.println("Game state authority: " + (authoritative ? "Server" : "Client"));
    }
    
    /**
     * Enable or disable client-side prediction
     */
    public void setClientPrediction(boolean enabled) {
        this.enableClientPrediction = enabled;
    }
    
    /**
     * Update game state (called every frame)
     */
    public void update() {
        long currentTime = System.currentTimeMillis();
        
        if (isAuthoritative) {
            // Server: Update authoritative state and broadcast
            updateAuthoritativeState();
            
            if (currentTime - lastSyncTime.get() >= SYNC_INTERVAL_MS) {
                broadcastGameState();
                lastSyncTime.set(currentTime);
            }
        } else {
            // Client: Apply prediction and handle reconciliation
            if (enableClientPrediction) {
                applyClientPrediction();
            }
            
            // Clean up old state history
            cleanupOldStates(currentTime);
        }
    }
    
    /**
     * Update the authoritative game state (server only)
     */
    private void updateAuthoritativeState() {
        if (!isAuthoritative) {
            return;
        }
        
        // Capture current player states
        Mario mario1 = gameEngine.getMario();
        Mario mario2 = gameEngine.getMario2();
        
        if (mario1 != null) {
            PlayerState state1 = NetworkUtils.marioToPlayerState(mario1);
            recordPlayerState("mario", state1, System.currentTimeMillis());
        }
        
        if (mario2 != null) {
            PlayerState state2 = NetworkUtils.marioToPlayerState(mario2);
            recordPlayerState("mario2", state2, System.currentTimeMillis());
        }
    }
    
    /**
     * Broadcast current game state to clients (server only)
     */
    private void broadcastGameState() {
        if (!isAuthoritative) {
            return;
        }
        
        try {
            Mario mario1 = gameEngine.getMario();
            Mario mario2 = gameEngine.getMario2();
            
            if (mario1 != null && mario2 != null) {
                PlayerState state1 = NetworkUtils.marioToPlayerState(mario1);
                PlayerState state2 = NetworkUtils.marioToPlayerState(mario2);
                
                // TODO: Send state through ConnectionManager
                // For now, just record the states locally
                recordPlayerState("mario", state1, System.currentTimeMillis());
                recordPlayerState("mario2", state2, System.currentTimeMillis());
            }
        } catch (Exception e) {
            System.err.println("Error broadcasting game state: " + e.getMessage());
        }
    }
    
    /**
     * Apply client-side prediction
     */
    private void applyClientPrediction() {
        if (isAuthoritative) {
            return;
        }
        
        // Get local player ID
        String localPlayerId = multiplayerManager.getCurrentMode() == GameMode.NETWORK_CLIENT ? "mario2" : "mario";
        
        // Apply prediction for local player only
        Mario localMario = localPlayerId.equals("mario") ? gameEngine.getMario() : gameEngine.getMario2();
        
        if (localMario != null) {
            // Simple prediction: continue current movement
            predictPlayerMovement(localMario);
        }
    }
    
    /**
     * Predict player movement based on current velocity
     */
    private void predictPlayerMovement(Mario mario) {
        if (mario == null) {
            return;
        }
        
        // Simple prediction: apply current velocity for one frame
        double predictedX = mario.getX() + mario.getVelX();
        double predictedY = mario.getY() + mario.getVelY();
        
        // Don't actually move the player, just record the prediction
        // Real movement is handled by the game engine
        PlayerState predictedState = new PlayerState(
            predictedX, predictedY,
            mario.getVelX(), mario.getVelY(),
            mario.isJumping(), mario.isFalling(), mario.getToRight(),
            mario.getRemainingLives(), mario.getPoints(), mario.getCoins()
        );
        
        recordPlayerState(mario.getWhichMario(), predictedState, System.currentTimeMillis());
    }
    
    /**
     * Handle incoming game state from server (client only)
     */
    public void handleServerGameState(PlayerState mario1State, PlayerState mario2State, long timestamp) {
        if (isAuthoritative) {
            return; // Server doesn't need to handle its own state
        }
        
        // Apply server state to non-local players
        String localPlayerId = multiplayerManager.getCurrentMode() == GameMode.NETWORK_CLIENT ? "mario2" : "mario";
        
        if (mario1State != null && !localPlayerId.equals("mario")) {
            applyPlayerState("mario", mario1State, timestamp);
        }
        
        if (mario2State != null && !localPlayerId.equals("mario2")) {
            applyPlayerState("mario2", mario2State, timestamp);
        }
        
        // Perform reconciliation for local player
        if (enableClientPrediction) {
            reconcileLocalPlayer(localPlayerId, 
                localPlayerId.equals("mario") ? mario1State : mario2State, timestamp);
        }
    }
    
    /**
     * Apply player state to the game
     */
    private void applyPlayerState(String playerId, PlayerState state, long timestamp) {
        Mario mario = playerId.equals("mario") ? gameEngine.getMario() : gameEngine.getMario2();
        
        if (mario != null && state != null) {
            NetworkUtils.applyPlayerStateToMario(state, mario);
            recordPlayerState(playerId, state, timestamp);
        }
    }
    
    /**
     * Reconcile local player state with server state
     */
    private void reconcileLocalPlayer(String playerId, PlayerState serverState, long serverTimestamp) {
        if (serverState == null) {
            return;
        }
        
        PlayerStateHistory history = playerStates.get(playerId);
        if (history == null) {
            return;
        }
        
        // Find the local state at the server timestamp
        PlayerState localState = history.getStateAtTime(serverTimestamp);
        
        if (localState != null) {
            // Check if reconciliation is needed
            double positionDiff = Math.abs(localState.getX() - serverState.getX()) + 
                                Math.abs(localState.getY() - serverState.getY());
            
            if (positionDiff > 5.0) { // 5 pixel tolerance
                System.out.println("Reconciling player " + playerId + " - position diff: " + positionDiff);
                
                // Apply server state
                applyPlayerState(playerId, serverState, serverTimestamp);
                
                // Re-apply any inputs that happened after the server timestamp
                // TODO: Implement input replay for better reconciliation
            }
        }
    }
    
    /**
     * Record player state in history
     */
    private void recordPlayerState(String playerId, PlayerState state, long timestamp) {
        playerStates.computeIfAbsent(playerId, k -> new PlayerStateHistory())
                   .addState(state, timestamp);
    }
    
    /**
     * Clean up old state history
     */
    private void cleanupOldStates(long currentTime) {
        for (PlayerStateHistory history : playerStates.values()) {
            history.cleanupOldStates(currentTime - STATE_TIMEOUT_MS);
        }
    }
    
    /**
     * Get current game state for a player
     */
    public PlayerState getPlayerState(String playerId) {
        PlayerStateHistory history = playerStates.get(playerId);
        return history != null ? history.getLatestState() : null;
    }
    
    /**
     * Check if game state is synchronized
     */
    public boolean isSynchronized() {
        if (isAuthoritative) {
            return true; // Server is always synchronized
        }
        
        long currentTime = System.currentTimeMillis();
        return (currentTime - lastSyncTime.get()) < (SYNC_INTERVAL_MS * 3); // Allow 3 frames of lag
    }
    
    /**
     * Get synchronization status for UI display
     */
    public String getSyncStatus() {
        if (isAuthoritative) {
            return "Server (Authoritative)";
        } else if (isSynchronized()) {
            return "Synchronized";
        } else {
            return "Desynchronized";
        }
    }
    
    /**
     * Reset game state manager
     */
    public void reset() {
        playerStates.clear();
        stateSequenceNumber.set(0);
        lastSyncTime.set(0);
    }
}
