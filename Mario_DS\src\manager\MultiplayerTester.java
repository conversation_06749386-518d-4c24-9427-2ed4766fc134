package manager;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import manager.network.NetworkUtils;

/**
 * Testing utility for validating multiplayer functionality.
 * Provides methods to test local and network multiplayer modes.
 */
public class MultiplayerTester {
    
    private final GameEngine gameEngine;
    private final MultiplayerManager multiplayerManager;
    
    public MultiplayerTester(GameEngine gameEngine) {
        this.gameEngine = gameEngine;
        this.multiplayerManager = gameEngine.getMultiplayerManager();
    }
    
    /**
     * Run comprehensive multiplayer tests
     */
    public TestResults runAllTests() {
        TestResults results = new TestResults();
        
        System.out.println("=== Starting Multiplayer Tests ===");
        
        // Test 1: Local Multiplayer
        results.localMultiplayerTest = testLocalMultiplayer();
        
        // Test 2: Network Host Setup
        results.networkHostTest = testNetworkHost();
        
        // Test 3: Network Client Connection
        results.networkClientTest = testNetworkClient();
        
        // Test 4: Game State Management
        results.gameStateTest = testGameStateManagement();
        
        // Test 5: Input Handling
        results.inputHandlingTest = testInputHandling();
        
        // Test 6: Cleanup and Resource Management
        results.cleanupTest = testCleanup();
        
        System.out.println("=== Multiplayer Tests Complete ===");
        results.printSummary();
        
        return results;
    }
    
    /**
     * Test local multiplayer functionality
     */
    public boolean testLocalMultiplayer() {
        System.out.println("Testing Local Multiplayer...");
        
        try {
            // Set local multiplayer mode
            multiplayerManager.setGameMode(GameMode.LOCAL_MULTIPLAYER);
            
            // Verify mode is set correctly
            if (multiplayerManager.getCurrentMode() != GameMode.LOCAL_MULTIPLAYER) {
                System.err.println("Failed to set local multiplayer mode");
                return false;
            }
            
            // Verify both players are configured
            if (!"mario".equals(multiplayerManager.getLocalPlayerId()) || 
                !"mario2".equals(multiplayerManager.getRemotePlayerId())) {
                System.err.println("Player IDs not configured correctly for local multiplayer");
                return false;
            }
            
            // Test control instructions
            String controls = multiplayerManager.getControlInstructions();
            if (!controls.contains("WASD") || !controls.contains("Arrow Keys")) {
                System.err.println("Control instructions missing for local multiplayer");
                return false;
            }
            
            System.out.println("✓ Local Multiplayer test passed");
            return true;
            
        } catch (Exception e) {
            System.err.println("Local Multiplayer test failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Test network host functionality
     */
    public boolean testNetworkHost() {
        System.out.println("Testing Network Host...");
        
        try {
            // Test IP address detection
            String localIP = NetworkUtils.getLocalIPAddress();
            if (localIP == null || localIP.isEmpty()) {
                System.err.println("Failed to detect local IP address");
                return false;
            }
            
            // Test port validation
            if (!NetworkUtils.isValidPort(12345)) {
                System.err.println("Port validation failed");
                return false;
            }
            
            // Set network host mode
            multiplayerManager.setGameMode(GameMode.NETWORK_HOST);
            
            // Verify mode is set correctly
            if (multiplayerManager.getCurrentMode() != GameMode.NETWORK_HOST) {
                System.err.println("Failed to set network host mode");
                return false;
            }
            
            // Verify host flag is set
            if (!multiplayerManager.isHost()) {
                System.err.println("Host flag not set correctly");
                return false;
            }
            
            // Test host info generation
            String hostInfo = multiplayerManager.getHostInfo();
            if (hostInfo.isEmpty()) {
                System.err.println("Host info not generated");
                return false;
            }
            
            System.out.println("✓ Network Host test passed");
            return true;
            
        } catch (Exception e) {
            System.err.println("Network Host test failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Test network client functionality
     */
    public boolean testNetworkClient() {
        System.out.println("Testing Network Client...");
        
        try {
            // Test IP address validation
            if (!NetworkUtils.isValidIPAddress("localhost")) {
                System.err.println("IP address validation failed for localhost");
                return false;
            }
            
            if (!NetworkUtils.isValidIPAddress("127.0.0.1")) {
                System.err.println("IP address validation failed for 127.0.0.1");
                return false;
            }
            
            if (NetworkUtils.isValidIPAddress("invalid.ip")) {
                System.err.println("IP address validation incorrectly passed for invalid IP");
                return false;
            }
            
            // Set network client mode
            multiplayerManager.setGameMode(GameMode.NETWORK_CLIENT);
            
            // Verify mode is set correctly
            if (multiplayerManager.getCurrentMode() != GameMode.NETWORK_CLIENT) {
                System.err.println("Failed to set network client mode");
                return false;
            }
            
            // Verify host flag is not set
            if (multiplayerManager.isHost()) {
                System.err.println("Host flag incorrectly set for client");
                return false;
            }
            
            System.out.println("✓ Network Client test passed");
            return true;
            
        } catch (Exception e) {
            System.err.println("Network Client test failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Test game state management
     */
    public boolean testGameStateManagement() {
        System.out.println("Testing Game State Management...");
        
        try {
            var gameStateManager = gameEngine.getGameStateManager();
            if (gameStateManager == null) {
                System.err.println("Game state manager not initialized");
                return false;
            }
            
            // Test authority configuration for different modes
            gameEngine.configureGameStateForMultiplayer(GameMode.LOCAL_MULTIPLAYER);
            gameEngine.configureGameStateForMultiplayer(GameMode.NETWORK_HOST);
            gameEngine.configureGameStateForMultiplayer(GameMode.NETWORK_CLIENT);
            
            // Test sync status
            String syncStatus = gameStateManager.getSyncStatus();
            if (syncStatus == null || syncStatus.isEmpty()) {
                System.err.println("Sync status not available");
                return false;
            }
            
            System.out.println("✓ Game State Management test passed");
            return true;
            
        } catch (Exception e) {
            System.err.println("Game State Management test failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Test input handling for different modes
     */
    public boolean testInputHandling() {
        System.out.println("Testing Input Handling...");
        
        try {
            // Test local multiplayer input
            multiplayerManager.setGameMode(GameMode.LOCAL_MULTIPLAYER);
            multiplayerManager.handleLocalInput(ButtonAction.GO_RIGHT, true);
            multiplayerManager.handleLocalInput(ButtonAction.GO_LEFT, false);
            
            // Test network mode input (should not throw exceptions)
            multiplayerManager.setGameMode(GameMode.NETWORK_HOST);
            multiplayerManager.handleLocalInput(ButtonAction.JUMP, true);
            
            multiplayerManager.setGameMode(GameMode.NETWORK_CLIENT);
            multiplayerManager.handleLocalInput(ButtonAction.FIRE, true);
            
            System.out.println("✓ Input Handling test passed");
            return true;
            
        } catch (Exception e) {
            System.err.println("Input Handling test failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Test cleanup and resource management
     */
    public boolean testCleanup() {
        System.out.println("Testing Cleanup...");
        
        try {
            // Test multiplayer manager cleanup
            multiplayerManager.cleanup();
            
            // Test game engine cleanup
            gameEngine.cleanup();
            
            System.out.println("✓ Cleanup test passed");
            return true;
            
        } catch (Exception e) {
            System.err.println("Cleanup test failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Test results container
     */
    public static class TestResults {
        public boolean localMultiplayerTest = false;
        public boolean networkHostTest = false;
        public boolean networkClientTest = false;
        public boolean gameStateTest = false;
        public boolean inputHandlingTest = false;
        public boolean cleanupTest = false;
        
        public boolean allPassed() {
            return localMultiplayerTest && networkHostTest && networkClientTest && 
                   gameStateTest && inputHandlingTest && cleanupTest;
        }
        
        public int getPassedCount() {
            int count = 0;
            if (localMultiplayerTest) count++;
            if (networkHostTest) count++;
            if (networkClientTest) count++;
            if (gameStateTest) count++;
            if (inputHandlingTest) count++;
            if (cleanupTest) count++;
            return count;
        }
        
        public void printSummary() {
            System.out.println("\n=== Test Results Summary ===");
            System.out.println("Local Multiplayer: " + (localMultiplayerTest ? "PASS" : "FAIL"));
            System.out.println("Network Host: " + (networkHostTest ? "PASS" : "FAIL"));
            System.out.println("Network Client: " + (networkClientTest ? "PASS" : "FAIL"));
            System.out.println("Game State Management: " + (gameStateTest ? "PASS" : "FAIL"));
            System.out.println("Input Handling: " + (inputHandlingTest ? "PASS" : "FAIL"));
            System.out.println("Cleanup: " + (cleanupTest ? "PASS" : "FAIL"));
            System.out.println("\nOverall: " + getPassedCount() + "/6 tests passed");
            
            if (allPassed()) {
                System.out.println("🎉 All multiplayer tests PASSED!");
            } else {
                System.out.println("⚠️  Some tests FAILED. Check implementation.");
            }
        }
    }
}
