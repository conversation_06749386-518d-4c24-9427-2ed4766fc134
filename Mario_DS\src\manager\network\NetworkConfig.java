package manager.network;

/**
 * Configuration settings for network optimization
 */
public class NetworkConfig {
    
    // Viewport culling settings
    public static final double DEFAULT_CULLING_BUFFER = 200.0; // pixels
    public static final double AGGRESSIVE_CULLING_BUFFER = 100.0; // More aggressive culling
    public static final double CONSERVATIVE_CULLING_BUFFER = 400.0; // Less aggressive culling
    
    // Sync frequency settings
    public static final long SYNC_60_FPS = 16; // 60 FPS
    public static final long SYNC_30_FPS = 33; // 30 FPS (recommended)
    public static final long SYNC_20_FPS = 50; // 20 FPS (low bandwidth)
    
    // Current settings (can be modified at runtime)
    private static double cullingBuffer = DEFAULT_CULLING_BUFFER;
    private static long syncInterval = SYNC_30_FPS;
    private static boolean cullingEnabled = true;
    
    /**
     * Get the current culling buffer distance
     */
    public static double getCullingBuffer() {
        return cullingBuffer;
    }
    
    /**
     * Set the culling buffer distance
     * @param buffer Buffer distance in pixels
     */
    public static void setCullingBuffer(double buffer) {
        cullingBuffer = Math.max(0, buffer); // Ensure non-negative
    }
    
    /**
     * Get the current sync interval
     */
    public static long getSyncInterval() {
        return syncInterval;
    }
    
    /**
     * Set the sync interval
     * @param interval Sync interval in milliseconds
     */
    public static void setSyncInterval(long interval) {
        syncInterval = Math.max(16, interval); // Minimum 16ms (60 FPS max)
    }
    
    /**
     * Check if culling is enabled
     */
    public static boolean isCullingEnabled() {
        return cullingEnabled;
    }
    
    /**
     * Enable or disable viewport culling
     * @param enabled Whether to enable culling
     */
    public static void setCullingEnabled(boolean enabled) {
        cullingEnabled = enabled;
    }
    
    /**
     * Set performance profile
     * @param profile Performance profile to use
     */
    public static void setPerformanceProfile(PerformanceProfile profile) {
        switch (profile) {
            case HIGH_QUALITY:
                setCullingBuffer(CONSERVATIVE_CULLING_BUFFER);
                setSyncInterval(SYNC_60_FPS);
                setCullingEnabled(true);
                break;
            case BALANCED:
                setCullingBuffer(DEFAULT_CULLING_BUFFER);
                setSyncInterval(SYNC_30_FPS);
                setCullingEnabled(true);
                break;
            case HIGH_PERFORMANCE:
                setCullingBuffer(AGGRESSIVE_CULLING_BUFFER);
                setSyncInterval(SYNC_20_FPS);
                setCullingEnabled(true);
                break;
            case NO_CULLING:
                setCullingEnabled(false);
                setSyncInterval(SYNC_30_FPS);
                break;
        }
    }
    
    /**
     * Get current configuration as a string
     */
    public static String getConfigString() {
        return String.format("NetworkConfig: Culling=%s, Buffer=%.0fpx, Sync=%dms (%.1f FPS)",
            cullingEnabled ? "ON" : "OFF",
            cullingBuffer,
            syncInterval,
            1000.0 / syncInterval
        );
    }
    
    /**
     * Performance profiles for different network conditions
     */
    public enum PerformanceProfile {
        HIGH_QUALITY,    // Best visual quality, higher bandwidth
        BALANCED,        // Good balance of quality and performance
        HIGH_PERFORMANCE, // Best performance, lower bandwidth
        NO_CULLING       // Disable culling (for debugging)
    }
}
