package test;

import manager.GameStatus;
import manager.ButtonAction;
import manager.MultiplayerManager;
import manager.GameEngine;

/**
 * Simple test to debug reconnection behavior
 */
public class ReconnectionDebugTest {

  public static void main(String[] args) {
    System.out.println("=== Reconnection Debug Test ===");

    // Create a mock game engine
    MockGameEngine gameEngine = new MockGameEngine();

    // Create a mock multiplayer manager
    MockMultiplayerManager multiplayerManager = new MockMultiplayerManager(gameEngine);

    // Test 1: Initial connection (should go to MAP_SELECTION)
    System.out.println("\n--- Test 1: Initial Connection ---");
    gameEngine.setGameStatus(GameStatus.START_SCREEN);
    System.out.println("Initial status: " + gameEngine.getGameStatus());

    multiplayerManager.onConnectionEstablished();
    System.out.println("Status after onConnectionEstablished: " + gameEngine.getGameStatus());
    System.out.println("Expected: MAP_SELECTION, Actual: " + gameEngine.getGameStatus());
    System.out.println("Test 1 " + (gameEngine.getGameStatus() == GameStatus.MAP_SELECTION ? "PASSED" : "FAILED"));

    // Test 2: Reconnection (should go to WAITING_FOR_SERVER)
    System.out.println("\n--- Test 2: Reconnection ---");
    gameEngine.setGameStatus(GameStatus.RUNNING); // Simulate we were running
                                                  // before
    System.out.println("Initial status: " + gameEngine.getGameStatus());

    // Simulate connection loss
    multiplayerManager.onConnectionLost();
    System.out.println("Status after onConnectionLost: " + gameEngine.getGameStatus());

    // Simulate reconnection
    multiplayerManager.onConnectionEstablished();
    System.out.println("Status after onConnectionEstablished: " + gameEngine.getGameStatus());
    System.out.println("Expected: WAITING_FOR_SERVER, Actual: " + gameEngine.getGameStatus());
    System.out.println("Test 2 " + (gameEngine.getGameStatus() == GameStatus.WAITING_FOR_SERVER ? "PASSED" : "FAILED"));

    // Test 3: Simulate single call scenario (after NetworkClient fix)
    System.out.println("\n--- Test 3: Single Call Scenario (Fixed) ---");
    gameEngine.setGameStatus(GameStatus.RUNNING); // Simulate we were running
                                                  // before
    System.out.println("Initial status: " + gameEngine.getGameStatus());

    // Simulate connection loss
    multiplayerManager.onConnectionLost();
    System.out.println("Status after onConnectionLost: " + gameEngine.getGameStatus());

    // Single call (only from ConnectionManager, NetworkClient no longer calls
    // this)
    multiplayerManager.onConnectionEstablished();
    System.out.println("Status after onConnectionEstablished: " + gameEngine.getGameStatus());
    System.out.println("Expected: WAITING_FOR_SERVER, Actual: " + gameEngine.getGameStatus());
    System.out.println("Test 3 " + (gameEngine.getGameStatus() == GameStatus.WAITING_FOR_SERVER ? "PASSED" : "FAILED"));

    // Test 4: Player Input During Reconnection
    System.out.println("\n--- Test 4: Player Input During Reconnection ---");
    gameEngine.setGameStatus(GameStatus.RUNNING);
    gameEngine.setMarioObjectsAvailable(true);
    System.out.println("Initial status: " + gameEngine.getGameStatus());

    // Simulate connection loss
    multiplayerManager.onConnectionLost();
    System.out.println("Status after onConnectionLost: " + gameEngine.getGameStatus());

    // Try to send input during reconnection state
    multiplayerManager.handleLocalInput(ButtonAction.M_JUMP, true);
    System.out.println("Input sent during RECONNECTING state");

    // Reconnect
    multiplayerManager.onConnectionEstablished();
    System.out.println("Status after reconnection: " + gameEngine.getGameStatus());

    // Try to send input during WAITING_FOR_SERVER state
    multiplayerManager.handleLocalInput(ButtonAction.M_RIGHT, true);
    System.out.println("Input sent during WAITING_FOR_SERVER state");

    // Verify input was received
    if (gameEngine.wasInputReceived()) {
      System.out.println("✅ PASSED: Player input works during reconnection states");
    } else {
      System.out.println("❌ FAILED: Player input blocked during reconnection states");
    }
  }

  static class MockGameEngine {
    private GameStatus gameStatus = GameStatus.START_SCREEN;
    private boolean marioObjectsAvailable = false;
    private boolean inputReceived = false;

    public GameStatus getGameStatus() {
      return gameStatus;
    }

    public void setGameStatus(GameStatus status) {
      System.out.println("  GameEngine.setGameStatus: " + this.gameStatus + " -> " + status);
      this.gameStatus = status;
    }

    public void setMarioObjectsAvailable(boolean available) {
      this.marioObjectsAvailable = available;
    }

    public Object getMario() {
      return marioObjectsAvailable ? new Object() : null;
    }

    public Object getMario2() {
      return marioObjectsAvailable ? new Object() : null;
    }

    public void receiveInputMario(ButtonAction action) {
      System.out.println("  MockGameEngine.receiveInputMario: " + action);
      inputReceived = true;
    }

    public void receiveInputMario2(ButtonAction action) {
      System.out.println("  MockGameEngine.receiveInputMario2: " + action);
      inputReceived = true;
    }

    public boolean wasInputReceived() {
      return inputReceived;
    }
  }

  static class MockMultiplayerManager {
    private final MockGameEngine gameEngine;
    private boolean isHost = false;
    private boolean isReconnecting = false;
    private String localPlayerId = "mario2";

    public MockMultiplayerManager(MockGameEngine gameEngine) {
      this.gameEngine = gameEngine;
    }

    public void onConnectionLost() {
      isReconnecting = true;
      System.out.println("  onConnectionLost called - setting isReconnecting = true");
      gameEngine.setGameStatus(GameStatus.RECONNECTING);
    }

    public void onConnectionEstablished() {
      String role = isHost ? "[Server]" : "[Client]";
      System.out.println("  " + role + " onConnectionEstablished called");
      System.out.println("  isReconnecting flag: " + isReconnecting);

      // Check if this is a reconnection scenario using our reliable flag
      if (isReconnecting) {
        System.out.println("  " + role + " Reconnection detected - waiting for server state sync");
        gameEngine.setGameStatus(GameStatus.WAITING_FOR_SERVER);
        isReconnecting = false; // Reset the flag after handling reconnection
      } else {
        // Initial connection - go to map selection
        System.out.println("  " + role + " Initial connection - switching to MAP_SELECTION");
        gameEngine.setGameStatus(GameStatus.MAP_SELECTION);
      }
    }

    public String getLocalPlayerId() {
      return localPlayerId;
    }

    public void setLocalPlayerId(String playerId) {
      this.localPlayerId = playerId;
    }

    public void handleLocalInput(ButtonAction action, boolean isPlayer1) {
      System.out
          .println("  MockMultiplayerManager.handleLocalInput: " + action + " for player " + (isPlayer1 ? "1" : "2"));
      if (isPlayer1 && localPlayerId.equals("mario")) {
        gameEngine.receiveInputMario(action);
      } else if (!isPlayer1 && localPlayerId.equals("mario2")) {
        gameEngine.receiveInputMario2(action);
      } else if (isPlayer1) {
        // Default to mario for player 1
        gameEngine.receiveInputMario(action);
      } else {
        // Default to mario2 for player 2
        gameEngine.receiveInputMario2(action);
      }
    }
  }
}
