package manager.ScoreManager;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

public class DatabaseConfig {
	private static Connection connection;

	static {
		initializeDatabase();
	}

	public static void testConnection() {
		try {
			getConnection();
			System.out.println("Database connection test passed");
		} catch (SQLException e) {
			System.err.println("Error testing database connection: " + e.getMessage());
			e.printStackTrace();
		}
	}

	public static Connection getConnection() throws SQLException {
		String jdbcUrl = "*********************************************************";
		String username = "postgres";
		String password = "bJiJfelOBWAwsoIWeypqgHcfpNFSoBYJ";

		if (connection == null || connection.isClosed()) {
			// Register the PostgreSQL driver
			try {
				Class.forName("org.postgresql.Driver");
				connection = DriverManager.getConnection(jdbcUrl, username, password);

				// Perform desired database operations
				System.out.println("Successfully connected to the database");

			} catch (ClassNotFoundException e) {
				System.err.println("PostgreSQL JDBC driver not found: " + e.getMessage());
			} catch (SQLException e) {
				System.err.println("Connection to database failed: " + e.getMessage());
			}
		}
		return connection;
	}

	private static void initializeDatabase() {
		try (Connection conn = getConnection();
				Statement stmt = conn.createStatement()) {

			// Create scores table if it doesn't exist
			String createTableSQL = """
					CREATE TABLE IF NOT EXISTS mario_scores (
					    id SERIAL PRIMARY KEY,
					    mario_score INTEGER NOT NULL,
					    mario2_score INTEGER NOT NULL,
					    total_score INTEGER NOT NULL,
					    game_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
					    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
					)
					""";

			stmt.executeUpdate(createTableSQL);
			System.out.println("Database initialized successfully");

		} catch (SQLException e) {
			System.err.println("Error initializing database: " + e.getMessage());
			e.printStackTrace();
		}
	}

	public static void closeConnection() {
		try {
			if (connection != null && !connection.isClosed()) {
				connection.close();
			}
		} catch (SQLException e) {
			System.err.println("Error closing database connection: " + e.getMessage());
		}
	}
}
