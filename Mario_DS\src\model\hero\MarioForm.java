package model.hero;

import views.Animation;
import views.ImageLoader;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;

public class MarioForm implements Serializable {
	public static final int SMALL = 0, SUPER = 1, FIRE = 2;
	public String whichMario;

	private transient Animation animation;
	private boolean isSuper, isFire; // note: fire form has priority over super form
	private transient BufferedImage fireballStyle;

	public MarioForm(Animation animation, boolean isSuper, boolean isFire, String whichMario) {
		this.animation = animation;
		this.isSuper = isSuper;
		this.isFire = isFire;
		this.whichMario = whichMario;

		ImageLoader imageLoader = new ImageLoader();
		BufferedImage fireball = imageLoader.loadImage("/sprite.png");
		fireballStyle = imageLoader.getSubImage(fireball, 3, 4, 24, 24);
	}

	public BufferedImage getCurrentStyle(boolean toRight, boolean movingInX, boolean movingInY) {

		BufferedImage style;

		if (movingInY && toRight) {
			style = animation.getRightFrames()[0];
		} else if (movingInY) {
			style = animation.getLeftFrames()[0];
		} else if (movingInX) {
			style = animation.animate(5, toRight);
		} else {
			if (toRight) {
				style = animation.getRightFrames()[1];
			} else
				style = animation.getLeftFrames()[1];
		}

		return style;
	}

	public MarioForm onTouchEnemy(ImageLoader imageLoader) {

		BufferedImage[] leftFrames;
		BufferedImage[] rightFrames;

		if (whichMario == "mario") {
			leftFrames = imageLoader.getLeftFrames(MarioForm.SMALL);
			rightFrames = imageLoader.getRightFrames(MarioForm.SMALL);
		} else {
			leftFrames = imageLoader.getLeftFrames2(MarioForm.SMALL);
			rightFrames = imageLoader.getRightFrames2(MarioForm.SMALL);
		}
		Animation newAnimation = new Animation(leftFrames, rightFrames);

		return new MarioForm(newAnimation, false, false, whichMario);

	}

	public Fireball fire(boolean toRight, double x, double y) {
		if (isFire) {
			return new Fireball(x, y + 48, fireballStyle, toRight, this.whichMario);
		}
		return null;
	}

	public boolean isSuper() {
		return isSuper;
	}

	public void setSuper(boolean isSuper) {
		this.isSuper = isSuper;
	}

	public boolean isFire() {
		return isFire;
	}

	public void setFire(boolean isFire) {
		this.isFire = isFire;
	}

	private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
		in.defaultReadObject();

		ImageLoader imageLoader = new ImageLoader();
		BufferedImage fireball = imageLoader.loadImage("/sprite.png");
		fireballStyle = imageLoader.getSubImage(fireball, 3, 4, 24, 24);

		BufferedImage[] leftFrames;
		BufferedImage[] rightFrames;

		int form = SMALL;
		if (isSuper)
			form = SUPER;
		if (isFire)
			form = FIRE;

		if (whichMario == "mario") {
			leftFrames = imageLoader.getLeftFrames(form);
			rightFrames = imageLoader.getRightFrames(form);
		} else {
			leftFrames = imageLoader.getLeftFrames2(form);
			rightFrames = imageLoader.getRightFrames2(form);
		}
		animation = new Animation(leftFrames, rightFrames);
	}
}
