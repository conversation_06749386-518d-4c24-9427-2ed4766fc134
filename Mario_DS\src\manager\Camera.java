package manager;

public class Camera {

  private double x, y;
  private static final double MAX_DISTANCE_MULTIPLAYER = 400;

  public Camera() {
    this.x = 0;
    this.y = 0;

  }

  public double getX() {
    return x;
  }

  public void setX(double x) {
    this.x = x;
  }

  public double getY() {
    return y;
  }

  public void setY(double y) {
    this.y = y;
  }

  public void shakeCamera() {
    // shaking = true;
    // frameNumber = 60;
  }

  public void moveCam(double xAmount) {
    x = x + xAmount;
  }

  /**
   * Enhanced camera positioning for multiplayer mode
   *
   * @param mario1X       Player 1 X position
   * @param mario2X       Player 2 X position
   * @param screenWidth   Screen width for camera bounds
   * @param isMultiplayer Whether we're in multiplayer mode
   * @return The optimal camera X position
   */
  public double calculateOptimalCameraPosition(double mario1X, double mario2X, int screenWidth, boolean isMultiplayer) {
    if (!isMultiplayer) {
      // Single player mode - follow only player 1
      return mario1X - screenWidth / 2;
    }

    double distance = Math.abs(mario1X - mario2X);

    if (distance <= MAX_DISTANCE_MULTIPLAYER) {
      // Players are close enough - use midpoint camera
      double minX = Math.min(mario1X, mario2X);
      double maxX = Math.max(mario1X, mario2X);
      double midpoint = (maxX + minX) / 2;
      return midpoint - screenWidth / 2;
    } else {
      // Players are too far apart - follow the rightmost player (leader)
      double leaderX = Math.max(mario1X, mario2X);
      return leaderX - screenWidth / 2;
    }
  }

  /**
   * Get the maximum distance allowed between players before camera behavior
   * changes
   */
  public static double getMaxDistanceMultiplayer() {
    return MAX_DISTANCE_MULTIPLAYER;
  }
}
