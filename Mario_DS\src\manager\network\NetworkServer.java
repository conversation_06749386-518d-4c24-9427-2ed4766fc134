package manager.network;

import java.io.*;
import java.net.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.List;
import java.awt.Rectangle;

import manager.ButtonAction;
import manager.MultiplayerManager;
import manager.network.NetworkMessage.*;
import manager.network.NetworkMessage.StatusSyncMessage;
import model.enemy.Enemy;

/**
 * Network server for hosting multiplayer games. Handles client connections,
 * input synchronization, and game state authority.
 */
public class NetworkServer {

  private static final int MAX_CLIENTS = 1; // Only support 2 players total
                                            // (host + 1 client)
  // Sync interval is now configurable through NetworkConfig
  private static final long PING_INTERVAL_MS = 5000; // 5 second ping interval

  private final MultiplayerManager multiplayerManager;
  private final int port;

  private ServerSocket serverSocket;
  private ExecutorService threadPool;
  private ScheduledExecutorService scheduler;
  private AtomicBoolean running;

  private ClientHandler connectedClient;
  private long lastSyncTime;
  private long lastPingTime;

  public NetworkServer(MultiplayerManager multiplayerManager, int port) {
    this.multiplayerManager = multiplayerManager;
    this.port = port;
    this.running = new AtomicBoolean(false);
    this.threadPool = Executors.newCachedThreadPool();
    this.scheduler = Executors.newScheduledThreadPool(2);
    this.lastSyncTime = 0;
    this.lastPingTime = 0;
  }

  /**
   * Start the server and begin accepting connections
   */
  public void start() throws IOException {
    if (running.get()) {
      return;
    }

    serverSocket = new ServerSocket(port);
    running.set(true);

    System.out.println("\u001B[32m[Server]\u001B[0m Started on port " + port);

    // Start accepting connections
    threadPool.submit(this::acceptConnections);

    // Start periodic sync and ping tasks
    scheduler.scheduleAtFixedRate(this::syncGameState, 0, NetworkConfig.getSyncInterval(), TimeUnit.MILLISECONDS);
    scheduler.scheduleAtFixedRate(this::sendPing, 0, PING_INTERVAL_MS, TimeUnit.MILLISECONDS);
  }

  /**
   * Stop the server and cleanup resources
   */
  public void stop() {
    if (!running.get()) {
      return;
    }

    running.set(false);

    try {
      if (serverSocket != null && !serverSocket.isClosed()) {
        serverSocket.close();
      }
    } catch (IOException e) {
      System.err.println("Error closing server socket: " + e.getMessage());
    }

    if (connectedClient != null) {
      connectedClient.disconnect();
    }

    threadPool.shutdown();
    scheduler.shutdown();

    try {
      if (!threadPool.awaitTermination(5, TimeUnit.SECONDS)) {
        threadPool.shutdownNow();
      }
      if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
        scheduler.shutdownNow();
      }
    } catch (InterruptedException e) {
      threadPool.shutdownNow();
      scheduler.shutdownNow();
    }

    System.out.println("\u001B[32m[Server]\u001B[0m Stopped");
  }

  /**
   * Accept incoming client connections
   */
  private void acceptConnections() {
    while (running.get()) {
      try {
        Socket clientSocket = serverSocket.accept();

        // if (connectedClient != null) {
        // // Already have a client, reject new connection
        // try {
        // ObjectOutputStream out = new
        // ObjectOutputStream(clientSocket.getOutputStream());
        // out.writeObject(new ConnectionResponseMessage(false, "Server full",
        // null));
        // out.flush();
        // clientSocket.close();
        // } catch (IOException e) {
        // System.err.println("Error rejecting client: " + e.getMessage());
        // }
        // } else {
        // Accept new client
        connectedClient = new ClientHandler(clientSocket);
        threadPool.submit(connectedClient);
        System.out
            .println("\u001B[32m[Server]\u001B[0m Client connected from " + clientSocket.getRemoteSocketAddress());
        // }

      } catch (IOException e) {
        if (running.get()) {
          System.err.println("Error accepting client connection: " + e.getMessage());
        }
      }
    }
  }

  /**
   * Broadcast player input to connected client
   */
  public void broadcastPlayerInput(String playerId, ButtonAction action, long timestamp) {
    if (connectedClient != null && connectedClient.isConnected()) {
      PlayerInputMessage message = new PlayerInputMessage(playerId, action, true);
      connectedClient.sendMessage(message);
    }
  }

  /**
   * Get the connected client handler
   */
  public ClientHandler getConnectedClient() {
    return connectedClient;
  }

  /**
   * Check if a client is connected
   */
  public boolean hasConnectedClient() {
    return connectedClient != null && connectedClient.isConnected();
  }

  /**
   * Sync game state with connected client
   */
  private void syncGameState() {
    if (connectedClient == null || !connectedClient.isConnected()) {
      return;
    }

    long currentTime = System.currentTimeMillis();
    if (currentTime - lastSyncTime < NetworkConfig.getSyncInterval()) {
      return;
    }

    try {
      // Get current game state from the game engine
      var gameEngine = multiplayerManager.getGameEngine();
      var gameStatus = gameEngine.getGameStatus();
      var mario1 = gameEngine.getMario();
      var mario2 = gameEngine.getMario2();

      System.out.println("\u001B[32m[Server]\u001B[0m Sync attempt - Status: " + gameStatus + ", Mario1: "
          + (mario1 != null ? "OK" : "NULL") + ", Mario2: " + (mario2 != null ? "OK" : "NULL"));

      // Only sync if both Mario objects are available (map is loaded)
      if (mario1 != null && mario2 != null) {
        System.out.println("\u001B[32m[Server]\u001B[0m Syncing game state to client");
        PlayerState mario1State = new PlayerState(mario1.getX(), mario1.getY(), mario1.getVelX(), mario1.getVelY(),
            mario1.isJumping(), mario1.isFalling(), mario1.getToRight(), mario1.getRemainingLives(), mario1.getPoints(),
            mario1.getCoins());

        PlayerState mario2State = new PlayerState(mario2.getX(), mario2.getY(), mario2.getVelX(), mario2.getVelY(),
            mario2.isJumping(), mario2.isFalling(), mario2.getToRight(), mario2.getRemainingLives(), mario2.getPoints(),
            mario2.getCoins());

        // Collect enemy states with viewport culling optimization
        java.util.List<EnemyState> enemyStates = new java.util.ArrayList<>();
        try {
          var mapManager = gameEngine.getMapManager();
          if (mapManager != null && mapManager.getMap() != null) {
            var allEnemies = mapManager.getMap().getEnemies();
            if (allEnemies != null) {
              List<Enemy> enemiesToSync;

              if (NetworkConfig.isCullingEnabled()) {
                // Calculate viewport culling bounds based on camera position
                var camera = gameEngine.getCamera();
                var screenSize = gameEngine.screenSize;
                Rectangle cullingBounds = ViewportCulling.calculateCullingBounds(camera, screenSize.width,
                    screenSize.height);

                // Filter enemies to only include those in viewport
                enemiesToSync = ViewportCulling.filterEnemiesInBounds(allEnemies, cullingBounds);

                // Log culling statistics and bandwidth savings
                ViewportCulling.logCullingStats(allEnemies.size(), enemiesToSync.size(), cullingBounds);
                int bandwidthSaved = ViewportCulling.calculateBandwidthSavings(allEnemies.size(), enemiesToSync.size());
                if (bandwidthSaved > 0) {
                  System.out.println(
                      "\u001B[33m[Culling]\u001B[0m Estimated bandwidth saved: " + bandwidthSaved + " bytes/sync");
                }
              } else {
                // No culling - sync all enemies
                enemiesToSync = allEnemies;
                System.out.println(
                    "\u001B[33m[Culling]\u001B[0m Culling disabled - syncing all " + allEnemies.size() + " enemies");
              }

              // Create enemy states for enemies to sync
              for (int i = 0; i < allEnemies.size(); i++) {
                var enemy = allEnemies.get(i);
                if (enemy != null && enemiesToSync.contains(enemy)) {
                  EnemyState enemyState = new EnemyState(i, // Use original
                                                            // index as enemy ID
                      enemy.getClass().getSimpleName(), enemy.getX(), enemy.getY(), enemy.getVelX(), enemy.getVelY(),
                      enemy.isFalling(), enemy.isJumping());
                  enemyStates.add(enemyState);

                  // Debug logging for enemy sync (reduced frequency)
                  if (enemyStates.size() <= 2) { // Only log first 2 visible
                                                 // enemies to reduce spam
                    System.out.println("\u001B[32m[Server]\u001B[0m Syncing enemy " + i + " ("
                        + enemy.getClass().getSimpleName() + ") at (" + enemy.getX() + "," + enemy.getY() + ")");
                  }
                }
              }
            }
          }
        } catch (Exception e) {
          System.err.println("\u001B[32m[Server]\u001B[0m Error collecting enemy states: " + e.getMessage());
        }

        GameStateMessage message = new GameStateMessage(mario1State, mario2State, gameEngine.getRemainingTime(),
            gameEngine.getGameStatus().toString().equals("RUNNING"), enemyStates);

        connectedClient.sendMessage(message);
        lastSyncTime = currentTime;
        System.out.println(
            "\u001B[32m[Server]\u001B[0m Game state sent successfully with " + enemyStates.size() + " enemies");
      } else {
        System.out.println("\u001B[32m[Server]\u001B[0m Skipping sync - Mario objects not ready");

        // If server has Mario objects but client doesn't, send status sync
        if (gameStatus.toString().equals("RUNNING")) {
          System.out.println("\u001B[32m[Server]\u001B[0m Server is RUNNING but client not ready, sending status sync");
          sendStatusSync();
        }
      }
      // If mario1 or mario2 is null, skip sync (normal during connection phase
      // before map selection)
    } catch (Exception e) {
      System.err.println("\u001B[31m[Server]\u001B[0m Error syncing game state: " + e.getMessage());
      e.printStackTrace();
    }
  }

  /**
   * Send ping to connected client
   */
  private void sendPing() {
    if (connectedClient != null && connectedClient.isConnected()) {
      long currentTime = System.currentTimeMillis();
      if (currentTime - lastPingTime >= PING_INTERVAL_MS) {
        PingMessage ping = new PingMessage(currentTime);
        connectedClient.sendMessage(ping);
        lastPingTime = currentTime;
      }
    }
  }

  /**
   * Send status synchronization to client
   */
  private void sendStatusSync() {
    if (connectedClient != null && connectedClient.isConnected()) {
      var gameEngine = multiplayerManager.getGameEngine();
      StatusSyncMessage statusSync = new StatusSyncMessage(gameEngine.getGameStatus().toString(),
          gameEngine.getSelectedMap());
      connectedClient.sendMessage(statusSync);
      System.out.println("\u001B[32m[Server]\u001B[0m Sent status sync: " + gameEngine.getGameStatus() + ", Map: "
          + gameEngine.getSelectedMap());
    }
  }

  /**
   * Handle client disconnection
   */
  private void onClientDisconnected() {
    connectedClient = null;
    System.out.println("\u001B[32m[Server]\u001B[0m Client disconnected, notifying multiplayer manager");
    multiplayerManager.onConnectionLost();
  }

  /**
   * Handles communication with a single client
   */
  private class ClientHandler implements Runnable {
    private final Socket socket;
    private ObjectInputStream input;
    private ObjectOutputStream output;
    private AtomicBoolean connected;

    public ClientHandler(Socket socket) {
      this.socket = socket;
      this.connected = new AtomicBoolean(false);
    }

    @Override
    public void run() {
      try {
        output = new ObjectOutputStream(socket.getOutputStream());
        input = new ObjectInputStream(socket.getInputStream());

        // Send connection acceptance
        ConnectionResponseMessage response = new ConnectionResponseMessage(true, "Connected", "mario2");
        output.writeObject(response);
        output.flush();

        connected.set(true);
        System.out.println("\u001B[32m[Server]\u001B[0m Client connection established, notifying multiplayer manager");
        multiplayerManager.onConnectionEstablished();

        // Handle incoming messages
        while (connected.get() && !socket.isClosed()) {
          try {
            NetworkMessage message = (NetworkMessage) input.readObject();
            handleMessage(message);
          } catch (ClassNotFoundException e) {
            System.err.println("Unknown message type received: " + e.getMessage());
          }
        }

      } catch (IOException e) {
        if (connected.get()) {
          System.err.println("Client communication error: " + e.getMessage());
        }
      } finally {
        disconnect();
      }
    }

    public void sendMessage(NetworkMessage message) {
      if (connected.get() && output != null) {
        try {
          output.writeObject(message);
          output.flush();
        } catch (IOException e) {
          System.err.println("Error sending message to client: " + e.getMessage());
          disconnect();
        }
      }
    }

    public boolean isConnected() {
      return connected.get() && !socket.isClosed();
    }

    public void disconnect() {
      if (connected.getAndSet(false)) {
        try {
          if (socket != null && !socket.isClosed()) {
            socket.close();
          }
        } catch (IOException e) {
          System.err.println("Error closing client socket: " + e.getMessage());
        }
        onClientDisconnected();
      }
    }

    private void handleMessage(NetworkMessage message) {
      switch (message.getType()) {
      case PLAYER_INPUT:
        PlayerInputMessage inputMsg = (PlayerInputMessage) message;
        System.out.println("\u001B[32m[Server]\u001B[0m Received input from client - Player: " + inputMsg.getPlayerId()
            + ", Action: " + inputMsg.getAction());
        // Forward remote player input to multiplayer manager
        multiplayerManager.handleRemotePlayerInput(inputMsg.getPlayerId(), inputMsg.getAction(),
            inputMsg.getTimestamp());
        break;

      case PONG:
        // Handle pong response (could calculate latency here)
        break;

      case DISCONNECT:
        System.out.println("\u001B[32m[Server]\u001B[0m Client requested disconnect");
        disconnect();
        break;

      default:
        System.out.println("\u001B[33m[Server]\u001B[0m Unhandled message type: " + message.getType());
        break;
      }
    }
  }
}
