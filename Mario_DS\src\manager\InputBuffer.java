package manager;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * Buffers player input for network synchronization and lag compensation.
 * Maintains a history of inputs for each player to handle network delays.
 */
public class InputBuffer {
    
    private static final int MAX_BUFFER_SIZE = 60; // 1 second at 60 FPS
    private static final long INPUT_TIMEOUT_MS = 1000; // 1 second timeout
    
    // Input history for each player
    private ConcurrentHashMap<String, ConcurrentLinkedQueue<InputEvent>> playerInputs;
    
    public InputBuffer() {
        this.playerInputs = new ConcurrentHashMap<>();
    }
    
    /**
     * Add an input event to the buffer
     */
    public void addInput(String playerId, ButtonAction action, long timestamp) {
        playerInputs.computeIfAbsent(playerId, k -> new ConcurrentLinkedQueue<>())
                   .offer(new InputEvent(action, timestamp, false));
        
        // Clean up old inputs
        cleanupOldInputs(playerId);
    }
    
    /**
     * Add a processed input event (from network)
     */
    public void addProcessedInput(String playerId, ButtonAction action, long timestamp) {
        playerInputs.computeIfAbsent(playerId, k -> new ConcurrentLinkedQueue<>())
                   .offer(new InputEvent(action, timestamp, true));
        
        // Clean up old inputs
        cleanupOldInputs(playerId);
    }
    
    /**
     * Get all unprocessed inputs for a player
     */
    public List<InputEvent> getUnprocessedInputs(String playerId) {
        List<InputEvent> unprocessed = new ArrayList<>();
        ConcurrentLinkedQueue<InputEvent> inputs = playerInputs.get(playerId);
        
        if (inputs != null) {
            for (InputEvent event : inputs) {
                if (!event.isProcessed()) {
                    unprocessed.add(event);
                }
            }
        }
        
        return unprocessed;
    }
    
    /**
     * Mark inputs as processed up to a certain timestamp
     */
    public void markProcessed(String playerId, long timestamp) {
        ConcurrentLinkedQueue<InputEvent> inputs = playerInputs.get(playerId);
        
        if (inputs != null) {
            for (InputEvent event : inputs) {
                if (event.getTimestamp() <= timestamp) {
                    event.setProcessed(true);
                }
            }
        }
    }
    
    /**
     * Get the most recent input for a player
     */
    public InputEvent getLatestInput(String playerId) {
        ConcurrentLinkedQueue<InputEvent> inputs = playerInputs.get(playerId);
        
        if (inputs != null && !inputs.isEmpty()) {
            InputEvent latest = null;
            for (InputEvent event : inputs) {
                if (latest == null || event.getTimestamp() > latest.getTimestamp()) {
                    latest = event;
                }
            }
            return latest;
        }
        
        return null;
    }
    
    /**
     * Clear all inputs for a player
     */
    public void clearInputs(String playerId) {
        ConcurrentLinkedQueue<InputEvent> inputs = playerInputs.get(playerId);
        if (inputs != null) {
            inputs.clear();
        }
    }
    
    /**
     * Clear all inputs for all players
     */
    public void clearAllInputs() {
        playerInputs.clear();
    }
    
    /**
     * Remove old inputs to prevent memory leaks
     */
    private void cleanupOldInputs(String playerId) {
        ConcurrentLinkedQueue<InputEvent> inputs = playerInputs.get(playerId);
        
        if (inputs != null) {
            long currentTime = System.currentTimeMillis();
            
            // Remove inputs older than timeout
            inputs.removeIf(event -> currentTime - event.getTimestamp() > INPUT_TIMEOUT_MS);
            
            // Limit buffer size
            while (inputs.size() > MAX_BUFFER_SIZE) {
                inputs.poll();
            }
        }
    }
    
    /**
     * Get input count for a player
     */
    public int getInputCount(String playerId) {
        ConcurrentLinkedQueue<InputEvent> inputs = playerInputs.get(playerId);
        return inputs != null ? inputs.size() : 0;
    }
    
    /**
     * Represents a single input event with timestamp and processing state
     */
    public static class InputEvent {
        private final ButtonAction action;
        private final long timestamp;
        private boolean processed;
        
        public InputEvent(ButtonAction action, long timestamp, boolean processed) {
            this.action = action;
            this.timestamp = timestamp;
            this.processed = processed;
        }
        
        public ButtonAction getAction() {
            return action;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        public boolean isProcessed() {
            return processed;
        }
        
        public void setProcessed(boolean processed) {
            this.processed = processed;
        }
        
        @Override
        public String toString() {
            return String.format("InputEvent{action=%s, timestamp=%d, processed=%s}", 
                               action, timestamp, processed);
        }
    }
}
