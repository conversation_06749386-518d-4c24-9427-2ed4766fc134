package manager.network;

import manager.*;
import manager.network.NetworkMessage.*;

/**
 * Test class for NetworkClient bug fixes, specifically testing the scenario
 * where the client gets stuck waiting for the server indefinitely.
 */
public class NetworkClientTest {

    /**
     * Test the fix for the bug where client gets stuck in WAITING_FOR_SERVER status
     * even when the server is ready and running.
     */
    public static void testClientStuckWaitingBugFix() {
        System.out.println("=== Testing NetworkClient Bug Fix ===");
        System.out.println("Testing scenario: Client stuck waiting for server when server is ready");
        
        try {
            // Create a mock game engine for testing
            MockGameEngine mockEngine = new MockGameEngine();
            MockMultiplayerManager mockMultiplayer = new MockMultiplayerManager(mockEngine);
            
            // Create a test network client
            TestableNetworkClient client = new TestableNetworkClient(mockMultiplayer, "localhost", 12345);
            
            // Test Case 1: Client receives game state with server running but Mario objects are null
            System.out.println("\n--- Test Case 1: Server running, client <PERSON> objects null ---");
            
            // Set initial client state to WAITING_FOR_SERVER
            mockEngine.setGameStatus(GameStatus.WAITING_FOR_SERVER);
            mockEngine.setSelectedMap(1);
            
            // Create a game state message indicating server is running
            PlayerState mario1State = new PlayerState(100, 200, 0, 0, false, false, true, 3, 1000, 5);
            PlayerState mario2State = new PlayerState(150, 200, 0, 0, false, false, true, 3, 500, 3);
            GameStateMessage gameStateMsg = new GameStateMessage(mario1State, mario2State, 300, true);
            
            // Simulate receiving the game state message
            client.simulateGameStateMessage(gameStateMsg);
            
            // Verify that the client transitioned from WAITING_FOR_SERVER
            if (mockEngine.getGameStatus() == GameStatus.WAITING_FOR_SERVER) {
                System.out.println("❌ FAILED: Client still stuck in WAITING_FOR_SERVER");
                return;
            } else {
                System.out.println("✅ PASSED: Client transitioned from WAITING_FOR_SERVER to " + mockEngine.getGameStatus());
            }
            
            // Test Case 2: Client receives status sync with RUNNING status
            System.out.println("\n--- Test Case 2: Status sync with RUNNING status ---");
            
            // Reset client to WAITING_FOR_SERVER
            mockEngine.setGameStatus(GameStatus.WAITING_FOR_SERVER);
            mockEngine.setSelectedMap(0); // Different map to trigger sync
            
            // Create status sync message with RUNNING status
            StatusSyncMessage statusSync = new StatusSyncMessage("RUNNING", 1);
            
            // Simulate receiving the status sync message
            client.simulateStatusSyncMessage(statusSync);
            
            // Verify that the client loaded the map and transitioned to appropriate status
            if (mockEngine.getSelectedMap() != 1) {
                System.out.println("❌ FAILED: Client did not sync map selection");
                return;
            }
            
            if (mockEngine.getGameStatus() == GameStatus.WAITING_FOR_SERVER) {
                System.out.println("❌ FAILED: Client still stuck in WAITING_FOR_SERVER after status sync");
                return;
            } else {
                System.out.println("✅ PASSED: Client synced map and transitioned to " + mockEngine.getGameStatus());
            }
            
            // Test Case 3: Verify map loading was triggered
            System.out.println("\n--- Test Case 3: Map loading verification ---");
            
            if (!mockEngine.wasSelectMapViaKeyboardCalled()) {
                System.out.println("❌ FAILED: selectMapViaKeyboard was not called to load the map");
                return;
            } else {
                System.out.println("✅ PASSED: selectMapViaKeyboard was called to load the map");
            }
            
            System.out.println("\n🎉 All NetworkClient bug fix tests PASSED!");
            
        } catch (Exception e) {
            System.err.println("❌ Test failed with exception: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Mock GameEngine for testing
     */
    static class MockGameEngine {
        private GameStatus gameStatus = GameStatus.START_SCREEN;
        private int selectedMap = 0;
        private boolean selectMapViaKeyboardCalled = false;
        
        public GameStatus getGameStatus() { return gameStatus; }
        public void setGameStatus(GameStatus status) { this.gameStatus = status; }
        
        public int getSelectedMap() { return selectedMap; }
        public void setSelectedMap(int map) { this.selectedMap = map; }
        
        public void selectMapViaKeyboard() { 
            selectMapViaKeyboardCalled = true;
            // Simulate successful map loading by setting status to RUNNING
            setGameStatus(GameStatus.RUNNING);
        }
        
        public boolean wasSelectMapViaKeyboardCalled() { return selectMapViaKeyboardCalled; }
        
        // Mock Mario objects - return null initially to simulate the bug scenario
        public Object getMario() { return null; }
        public Object getMario2() { return null; }
    }
    
    /**
     * Mock MultiplayerManager for testing
     */
    static class MockMultiplayerManager {
        private final MockGameEngine gameEngine;
        
        public MockMultiplayerManager(MockGameEngine gameEngine) {
            this.gameEngine = gameEngine;
        }
        
        public MockGameEngine getGameEngine() { return gameEngine; }
    }
    
    /**
     * Testable version of NetworkClient that exposes message handling methods
     */
    static class TestableNetworkClient {
        private final MockMultiplayerManager multiplayerManager;
        
        public TestableNetworkClient(MockMultiplayerManager multiplayerManager, String address, int port) {
            this.multiplayerManager = multiplayerManager;
        }
        
        /**
         * Simulate receiving a game state message (copy of the fixed handleGameStateUpdate logic)
         */
        public void simulateGameStateMessage(GameStateMessage message) {
            try {
                var gameEngine = multiplayerManager.getGameEngine();
                var gameStatus = gameEngine.getGameStatus();
                
                var mario1 = gameEngine.getMario();
                var mario2 = gameEngine.getMario2();
                
                System.out.println("[Client] Received game state - Status: " + gameStatus + ", Mario1: "
                    + (mario1 != null ? "OK" : "NULL") + ", Mario2: " + (mario2 != null ? "OK" : "NULL") 
                    + ", Server running: " + message.isGameRunning());
                
                // Check if server is running but client hasn't loaded the map yet
                if (message.isGameRunning() && (mario1 == null || mario2 == null)) {
                    System.out.println("[Client] Server is running but client map not loaded - triggering map load");
                    
                    // If client is still waiting for server, transition to map selection to load the map
                    if (gameStatus == GameStatus.WAITING_FOR_SERVER) {
                        System.out.println("[Client] Transitioning from WAITING_FOR_SERVER to MAP_SELECTION");
                        gameEngine.setGameStatus(GameStatus.MAP_SELECTION);
                    }
                    
                    // Try to load the map if we're in map selection and server is running
                    if (gameStatus == GameStatus.MAP_SELECTION || gameStatus == GameStatus.WAITING_FOR_SERVER) {
                        System.out.println("[Client] Attempting to load map to sync with running server");
                        gameEngine.selectMapViaKeyboard();
                    }
                }
                
            } catch (Exception e) {
                System.err.println("[Client] Error handling game state update: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        /**
         * Simulate receiving a status sync message (copy of the fixed handleStatusSync logic)
         */
        public void simulateStatusSyncMessage(StatusSyncMessage message) {
            System.out.println("[Client] Received status sync - Status: " + message.getGameStatus()
                + ", Map: " + message.getSelectedMap());
            
            var gameEngine = multiplayerManager.getGameEngine();
            var currentStatus = gameEngine.getGameStatus();
            
            try {
                GameStatus serverStatus = GameStatus.valueOf(message.getGameStatus());
                System.out.println("[Client] Syncing status from " + currentStatus + " to " + serverStatus);
                
                // If server is running a specific map, load it on client
                if (serverStatus == GameStatus.RUNNING || serverStatus == GameStatus.MAP_SELECTION) {
                    int serverMap = message.getSelectedMap();
                    System.out.println("[Client] Server map: " + serverMap + ", Client map: " + gameEngine.getSelectedMap());
                    
                    if (serverMap != gameEngine.getSelectedMap()) {
                        System.out.println("[Client] Syncing map selection to " + serverMap);
                        gameEngine.setSelectedMap(serverMap);
                    }
                    
                    // If server is running and client is still waiting or in map selection, load the map
                    if (serverStatus == GameStatus.RUNNING && 
                        (currentStatus == GameStatus.WAITING_FOR_SERVER || currentStatus == GameStatus.MAP_SELECTION)) {
                        System.out.println("[Client] Server is running - loading map and starting game");
                        
                        // First transition to MAP_SELECTION if we're still waiting
                        if (currentStatus == GameStatus.WAITING_FOR_SERVER) {
                            gameEngine.setGameStatus(GameStatus.MAP_SELECTION);
                        }
                        
                        // Load the map
                        gameEngine.selectMapViaKeyboard();
                    } else {
                        // Update status after map handling
                        gameEngine.setGameStatus(serverStatus);
                    }
                } else {
                    // For other statuses, just sync directly
                    gameEngine.setGameStatus(serverStatus);
                }
            } catch (IllegalArgumentException e) {
                System.err.println("[Client] Invalid game status from server: " + message.getGameStatus());
            }
        }
    }
    
    /**
     * Main method to run the test
     */
    public static void main(String[] args) {
        testClientStuckWaitingBugFix();
    }
}
