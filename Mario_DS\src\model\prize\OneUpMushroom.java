package model.prize;

import manager.GameEngine;
import model.hero.Mario;


import java.awt.image.BufferedImage;

public class OneUpMushroom extends BoostItem {

	public OneUpMushroom(double x, double y, BufferedImage style) {
		super(x, y, style);
		setPoint(200);
	}

	@Override
	public void onTouch(<PERSON>, GameEngine engine) {
		mario.acquirePoints(getPoint());
		mario.setRemainingLives(mario.getRemainingLives() + 1);
		engine.playOneUp();
	}

	@Override
	public void onTouch2(<PERSON> mario2, GameEngine engine) {
		mario2.acquirePoints(getPoint());
		mario2.setRemainingLives(mario2.getRemainingLives() + 1);
		engine.playOneUp();
	}
}
