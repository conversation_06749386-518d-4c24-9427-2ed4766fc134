package test;

import manager.network.NetworkConfig;
import manager.network.NetworkConfig.PerformanceProfile;

/**
 * Demonstration of network optimization features
 */
public class NetworkOptimizationDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Network Optimization Demo ===");
        
        // Show default configuration
        System.out.println("\n--- Default Configuration ---");
        System.out.println(NetworkConfig.getConfigString());
        
        // Test different performance profiles
        System.out.println("\n--- Performance Profiles ---");
        
        PerformanceProfile[] profiles = {
            PerformanceProfile.HIGH_QUALITY,
            PerformanceProfile.BALANCED,
            PerformanceProfile.HIGH_PERFORMANCE,
            PerformanceProfile.NO_CULLING
        };
        
        for (PerformanceProfile profile : profiles) {
            NetworkConfig.setPerformanceProfile(profile);
            System.out.println(profile + ": " + NetworkConfig.getConfigString());
        }
        
        // Demonstrate bandwidth calculations
        System.out.println("\n--- Bandwidth Savings Simulation ---");
        
        // Simulate different scenarios
        int[] enemyCounts = {10, 50, 100, 200};
        double[] cullingRatios = {0.3, 0.5, 0.7}; // 30%, 50%, 70% of enemies visible
        
        System.out.println("Scenario Analysis (30 FPS sync rate):");
        System.out.println("Enemies | Visible | Bandwidth/sec | Savings/sec");
        System.out.println("--------|---------|---------------|------------");
        
        for (int enemyCount : enemyCounts) {
            for (double ratio : cullingRatios) {
                int visibleEnemies = (int) (enemyCount * ratio);
                int culledEnemies = enemyCount - visibleEnemies;
                
                // Calculate bandwidth (50 bytes per enemy state)
                int totalBandwidth = enemyCount * 50 * 30; // 30 FPS
                int usedBandwidth = visibleEnemies * 50 * 30;
                int savedBandwidth = culledEnemies * 50 * 30;
                
                System.out.printf("%7d | %7d | %10s | %10s%n",
                    enemyCount, visibleEnemies,
                    formatBytes(usedBandwidth),
                    formatBytes(savedBandwidth));
            }
        }
        
        // Show optimization recommendations
        System.out.println("\n--- Optimization Recommendations ---");
        System.out.println("1. Use BALANCED profile for most games (good performance + quality)");
        System.out.println("2. Use HIGH_PERFORMANCE for slow connections or large maps");
        System.out.println("3. Use HIGH_QUALITY for small maps with few enemies");
        System.out.println("4. Adjust buffer distance based on game speed:");
        System.out.println("   - Fast-moving enemies: Increase buffer (300-400px)");
        System.out.println("   - Slow-moving enemies: Decrease buffer (100-200px)");
        
        // Performance impact analysis
        System.out.println("\n--- Performance Impact Analysis ---");
        System.out.println("Before optimization (sync all enemies):");
        System.out.println("- 100 enemies × 50 bytes × 60 FPS = 300 KB/sec");
        System.out.println("- High CPU usage for serialization");
        System.out.println("- Network congestion on slow connections");
        
        System.out.println("\nAfter optimization (viewport culling + 30 FPS):");
        System.out.println("- ~30 visible enemies × 50 bytes × 30 FPS = 45 KB/sec");
        System.out.println("- 85% bandwidth reduction");
        System.out.println("- 50% CPU reduction (lower sync rate)");
        System.out.println("- Smooth gameplay on most connections");
        
        // Reset to balanced profile
        NetworkConfig.setPerformanceProfile(PerformanceProfile.BALANCED);
        System.out.println("\n--- Final Configuration ---");
        System.out.println(NetworkConfig.getConfigString());
        
        System.out.println("\n=== Demo Complete ===");
    }
    
    private static String formatBytes(int bytes) {
        if (bytes < 1024) {
            return bytes + "B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1fKB", bytes / 1024.0);
        } else {
            return String.format("%.1fMB", bytes / (1024.0 * 1024.0));
        }
    }
}
