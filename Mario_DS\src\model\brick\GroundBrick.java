package model.brick;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.ObjectInputStream;

import views.ImageLoader;

public class GroundBrick extends Brick {

  public GroundBrick(double x, double y, BufferedImage style) {
    super(x, y, style);
    setBreakable(false);
    setEmpty(true);
  }

  private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
    in.defaultReadObject();
    ImageLoader imageLoader = new ImageLoader();
    BufferedImage sprite = imageLoader.loadImage("/sprite.png");
    setStyle(imageLoader.getSubImage(sprite, 2, 2, 48, 48));
  }
}
